# Flask Configuration
SECRET_KEY=your-secret-key-here-change-in-production
FLASK_ENV=production
FLASK_DEBUG=False

# MongoDB Configuration
MONGO_URI=mongodb://localhost:27017/personal_organizer

# Frontend URL (for CORS)
FRONTEND_URL=https://your-domain.com

# Session Security
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax

# Rate Limiting Storage
# Use 'memory://' for in-memory storage or Redis URL for persistent storage
REDIS_URI=memory://

# Weather API Configuration
# Open-Meteo doesn't require API key
WEATHER_API_URL=https://api.open-meteo.com/v1/forecast

# Weather Cities Coordinates
STL_LAT=38.6270
STL_LON=-90.1994
ACCRA_LAT=5.6037
ACCRA_LON=-0.1870

# Admin User Initial Setup
# Only used for first-time setup
ADMIN_USERNAME=admin
ADMIN_PASSWORD=change-this-password-immediately

# Gunicorn Configuration
BIND_ADDRESS=127.0.0.1:5000
WORKERS=4

# Timezone Configuration
DEFAULT_TIMEZONE=America/Chicago