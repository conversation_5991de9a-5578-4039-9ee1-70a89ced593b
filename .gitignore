# Dependencies
node_modules/
venv/
env/
.venv/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
pip-log.txt
pip-delete-this-directory.txt

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
*.sublime-project

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Build outputs
dist/
build/
*.egg-info/
.eggs/
*.egg

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Testing
coverage/
*.cover
.hypothesis/
.pytest_cache/
.coverage
.coverage.*
htmlcov/
.tox/
.nox/

# Production
*.pid
*.seed
*.pid.lock

# Frontend build
dist-ssr/
*.local

# Editor directories and files
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Python
*.py[cod]
*$py.class
*.so
.Python
develop-eggs/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Flask
instance/
.webassets-cache

# MongoDB
*.lock
*.bson

# Temporary files
tmp/
temp/
*.tmp
*.temp
*.bak
*.backup
*.old

# SSL certificates
*.pem
*.key
*.crt
*.csr

# Package files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# Virtual machine files
*.vmdk
*.vmx
*.vmxf
*.vmsd
*.vmsn
*.vswp

# Cloudflare
.cloudflared/

# Personal Organizer specific
/var/log/personal-organizer/
personal-organizer.log
audit.log

# Database dumps
*.dump
*.sql
backup/

# Static files (if generated)
staticfiles/
static/admin/
static/CACHE/

# Media files (if any uploaded)
media/
uploads/

# Local development
local_settings.py
dev.db
test.db

# Documentation build
docs/_build/
site/

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# Celery (if used later)
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.spyderproject
.spyproject
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/