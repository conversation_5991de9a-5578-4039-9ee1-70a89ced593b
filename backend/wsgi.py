"""
WSGI entry point for Gunicorn
Production server configuration for Personal Organizer
"""

import os
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).resolve().parent
sys.path.insert(0, str(backend_dir))

from app import app

# Gunicorn looks for an 'application' variable
application = app

if __name__ == "__main__":
    # This allows running with python wsgi.py for testing
    # In production, use: gunicorn wsgi:application
    app.run()