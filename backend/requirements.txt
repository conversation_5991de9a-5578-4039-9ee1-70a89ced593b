# Core Flask framework
Flask==3.0.0
Flask-PyMongo==2.3.0
Flask-Login==0.6.3
Flask-CORS==4.0.0
Flask-Limiter==3.5.0

# Security and authentication
Werkzeug==3.0.1
python-decouple==3.8
nh3==0.2.15

# Database
pymongo==4.6.1

# Date and time handling
python-dateutil==2.8.2
pytz==2023.3

# HTTP requests for weather API
requests==2.31.0

# WSGI server
gunicorn==21.2.0

# Redis for rate limiting (optional, can use memory)
redis==5.0.1

# For production deployment
python-dotenv==1.0.0