"""
Authentication logic for Personal Organizer
Handles user login, session management, and authentication decorators
"""

from flask_login import UserMixin
from werkzeug.security import check_password_hash, generate_password_hash
from bson import ObjectId
from bson.errors import InvalidId
from datetime import datetime, timezone
from functools import wraps
from flask import jsonify
from flask_login import current_user
import logging

logger = logging.getLogger(__name__)

class User(UserMixin):
    """User class for Flask-Login integration"""
    
    def __init__(self, user_data):
        """Initialize user from MongoDB document"""
        self.id = str(user_data['_id'])
        self.username = user_data['username']
        self.password_hash = user_data.get('password_hash')
        self.email = user_data.get('email')
        self.phone = user_data.get('phone')
        self.dashboard_layout = user_data.get('dashboard_layout')
        self.categories = user_data.get('categories', [])
        self.theme = user_data.get('theme', 'light')
        self.created_at = user_data.get('created_at')
        self.updated_at = user_data.get('updated_at')
        self._user_data = user_data
    
    def get_id(self):
        """Return user ID as string for Flask-Login"""
        return self.id
    
    def check_password(self, password):
        """Check if provided password matches user's password"""
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        """Convert user to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'phone': self.phone,
            'theme': self.theme,
            'categories': self.categories,
            'dashboard_layout': self.dashboard_layout
        }
    
    @property
    def is_authenticated(self):
        """Check if user is authenticated"""
        return True
    
    @property
    def is_active(self):
        """Check if user is active"""
        return True
    
    @property
    def is_anonymous(self):
        """Check if user is anonymous"""
        return False

def load_user(mongo, user_id):
    """Load user by ID for Flask-Login"""
    try:
        user_data = mongo.db.users.find_one({'_id': ObjectId(user_id)})
        if user_data:
            return User(user_data)
    except InvalidId:
        logger.warning(f"Invalid user ID format: {user_id}")
    except Exception as e:
        logger.error(f"Error loading user {user_id}: {e}")
    return None

def authenticate_user(mongo, username, password):
    """Authenticate user with username and password"""
    user_data = mongo.db.users.find_one({'username': username})
    
    if not user_data:
        return None
    
    user = User(user_data)
    
    if user.check_password(password):
        # Update last login time
        mongo.db.users.update_one(
            {'_id': ObjectId(user.id)},
            {'$set': {'last_login': datetime.now(timezone.utc)}}
        )
        return user
    
    return None

def create_initial_admin(mongo, app):
    """Create initial admin user if no users exist"""
    user_count = mongo.db.users.count_documents({})
    
    if user_count == 0:
        app.logger.info("No users found. Creating initial admin user...")
        
        admin_doc = {
            'username': app.config['ADMIN_USERNAME'],
            'password_hash': generate_password_hash(app.config['ADMIN_PASSWORD']),
            'email': None,
            'phone': None,
            'dashboard_layout': app.config['DEFAULT_DASHBOARD_LAYOUT'],
            'categories': app.config['DEFAULT_CATEGORIES'],
            'theme': 'light',
            'created_at': datetime.now(timezone.utc),
            'updated_at': datetime.now(timezone.utc)
        }
        
        result = mongo.db.users.insert_one(admin_doc)
        
        # Create default contact types for admin
        from models import ContactTypeModel
        ContactTypeModel.create_defaults(mongo.db, result.inserted_id)
        
        app.logger.info(f"Admin user created with username: {app.config['ADMIN_USERNAME']}")
        app.logger.warning("Please change the admin password immediately!")
    
    return True

def login_required_json(f):
    """Custom login_required decorator that returns JSON response"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return jsonify({'error': 'Authentication required'}), 401
        return f(*args, **kwargs)
    return decorated_function

def refresh_weather_on_login(mongo):
    """Check if weather needs refresh on login"""
    from datetime import datetime, timedelta
    from models import BaseModel
    
    # Check if weather cache is older than 2 hours
    for city in ['stl', 'accra']:
        cache = mongo.db.weather_cache.find_one({'_id': city})
        
        # Ensure fetched_at is timezone-aware before comparison
        fetched_at_aware = BaseModel.ensure_utc_aware(cache.get('fetched_at')) if cache else None
        if not cache or not fetched_at_aware or (BaseModel.utc_now() - fetched_at_aware) > timedelta(hours=2):
            # Return True to indicate weather refresh is needed
            return True
    
    return False

def validate_password_strength(password):
    """Validate password meets minimum requirements"""
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    
    # Add more validation rules as needed
    # For now, just check length for a personal app
    
    return True, "Password is valid"

def hash_password(password):
    """Hash password with current best practices"""
    # Werkzeug's generate_password_hash uses PBKDF2 with 600k+ iterations by default
    return generate_password_hash(password)

def init_auth(app, mongo):
    """Initialize authentication for the app"""
    from flask_login import LoginManager
    
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    
    @login_manager.user_loader
    def user_loader(user_id):
        return load_user(mongo, user_id)
    
    # Create initial admin if needed
    with app.app_context():
        create_initial_admin(mongo, app)
    
    return login_manager