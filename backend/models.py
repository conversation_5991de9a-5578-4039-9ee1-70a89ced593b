"""
MongoDB models and database operations for Personal Organizer
"""

from datetime import datetime, timezone
from bson import ObjectId
from werkzeug.security import generate_password_hash
import pytz
import logging

logger = logging.getLogger(__name__)

class BaseModel:
    """Base model with common operations"""
    
    @staticmethod
    def serialize_doc(doc):
        """Convert MongoDB document to JSON-serializable format"""
        if not doc:
            return None
        
        # Convert ObjectId to string
        if '_id' in doc:
            doc['_id'] = str(doc['_id'])
        
        # Convert any ObjectId fields
        for key, value in doc.items():
            if isinstance(value, ObjectId):
                doc[key] = str(value)
            elif isinstance(value, datetime):
                doc[key] = value.isoformat()
            elif isinstance(value, list):
                # Handle lists of ObjectIds, dates, or nested dictionaries
                doc[key] = [
                    str(item) if isinstance(item, ObjectId) else
                    item.isoformat() if isinstance(item, datetime) else
                    BaseModel.serialize_doc(item) if isinstance(item, dict) else
                    item
                    for item in value
                ]
        
        return doc
    
    @staticmethod
    def utc_now():
        """Get current UTC time"""
        return datetime.now(timezone.utc)
    
    @staticmethod
    def to_chicago_time(utc_dt):
        """Convert UTC datetime to Chicago timezone"""
        if utc_dt is None:
            return None
        
        if utc_dt.tzinfo is None:
            utc_dt = utc_dt.replace(tzinfo=timezone.utc)
        
        chicago_tz = pytz.timezone('America/Chicago')
        return utc_dt.astimezone(chicago_tz)

    @staticmethod
    def ensure_utc_aware(dt):
        """Ensure a datetime object is UTC timezone-aware.
        If it's naive, assume it's UTC and make it aware.
        """
        if dt is None:
            return None
        if dt.tzinfo is None:
            return dt.replace(tzinfo=timezone.utc)
        return dt.astimezone(timezone.utc)  # Convert to UTC if already aware but different timezone

class UserModel(BaseModel):
    """User model operations"""
    
    @staticmethod
    def create_user(db, username, password, email=None, phone=None):
        """Create a new user"""
        from config import Config
        
        user_doc = {
            'username': username,
            'password_hash': generate_password_hash(password),
            'email': email,
            'phone': phone,
            'dashboard_layout': Config.DEFAULT_DASHBOARD_LAYOUT,
            'categories': Config.DEFAULT_CATEGORIES,
            'theme': 'light',
            'created_at': BaseModel.utc_now(),
            'updated_at': BaseModel.utc_now()
        }
        
        result = db.users.insert_one(user_doc)
        user_doc['_id'] = result.inserted_id
        
        # Create default contact types for the user
        ContactTypeModel.create_defaults(db, result.inserted_id)
        
        return BaseModel.serialize_doc(user_doc)
    
    @staticmethod
    def find_by_username(db, username):
        """Find user by username"""
        user = db.users.find_one({'username': username})
        return BaseModel.serialize_doc(user)
    
    @staticmethod
    def find_by_id(db, user_id):
        """Find user by ID"""
        user = db.users.find_one({'_id': ObjectId(user_id)})
        return BaseModel.serialize_doc(user)
    
    @staticmethod
    def update_user(db, user_id, update_data):
        """Update user data"""
        update_data['updated_at'] = BaseModel.utc_now()
        
        result = db.users.update_one(
            {'_id': ObjectId(user_id)},
            {'$set': update_data}
        )
        
        return result.modified_count > 0

class ContactTypeModel(BaseModel):
    """Contact type model operations"""
    
    @staticmethod
    def create_defaults(db, user_id):
        """Create default contact types for a user"""
        from config import Config
        
        contact_types = []
        for ct in Config.DEFAULT_CONTACT_TYPES:
            doc = {
                'user_id': ObjectId(user_id),
                'label': ct['label'],
                'threshold_days': ct['threshold_days'],
                'created_at': BaseModel.utc_now()
            }
            contact_types.append(doc)
        
        if contact_types:
            db.contact_types.insert_many(contact_types)
    
    @staticmethod
    def get_user_contact_types(db, user_id):
        """Get all contact types for a user"""
        types = db.contact_types.find({'user_id': ObjectId(user_id)})
        return [BaseModel.serialize_doc(t) for t in types]

class ContactModel(BaseModel):
    """Contact model operations"""
    
    @staticmethod
    def create_contact(db, user_id, type_id, name, last_contact=None, notes=None):
        """Create a new contact"""
        doc = {
            'user_id': ObjectId(user_id),
            'type_id': ObjectId(type_id),
            'name': name,
            'last_contact': last_contact or BaseModel.utc_now(),
            'notes': notes,
            'created_at': BaseModel.utc_now(),
            'updated_at': BaseModel.utc_now()
        }
        
        result = db.contacts.insert_one(doc)
        doc['_id'] = result.inserted_id
        return BaseModel.serialize_doc(doc)
    
    @staticmethod
    def get_user_contacts_with_status(db, user_id):
        """Get all contacts with their color status"""
        # Aggregate to join with contact types
        pipeline = [
            {'$match': {'user_id': ObjectId(user_id)}},
            {
                '$lookup': {
                    'from': 'contact_types',
                    'localField': 'type_id',
                    'foreignField': '_id',
                    'as': 'type'
                }
            },
            {'$unwind': '$type'},
            {'$sort': {'last_contact': -1}}
        ]
        
        contacts = list(db.contacts.aggregate(pipeline))
        
        # Calculate color status for each contact
        now = BaseModel.utc_now()
        for contact in contacts:
            threshold = contact['type']['threshold_days']
            # Ensure last_contact is timezone-aware before comparison
            last_contact_aware = BaseModel.ensure_utc_aware(contact['last_contact'])
            days_since = (now - last_contact_aware).days
            
            if days_since <= threshold:
                contact['status'] = 'green'
            elif days_since <= threshold * 2:
                contact['status'] = 'yellow'
            else:
                contact['status'] = 'red'
            
            contact['days_since_contact'] = days_since
        
        return [BaseModel.serialize_doc(c) for c in contacts]
    
    @staticmethod
    def update_last_contact(db, contact_id, user_id):
        """Update the last contact date to now"""
        result = db.contacts.update_one(
            {'_id': ObjectId(contact_id), 'user_id': ObjectId(user_id)},
            {
                '$set': {
                    'last_contact': BaseModel.utc_now(),
                    'updated_at': BaseModel.utc_now()
                }
            }
        )
        return result.modified_count > 0

class BillModel(BaseModel):
    """Bill model operations"""
    
    @staticmethod
    def create_bill(db, user_id, title, due_date=None, rrule=None, 
                   amount=None, category=None, notes=None):
        """Create a new bill or vacation"""
        doc = {
            'user_id': ObjectId(user_id),
            'title': title,
            'due_date': due_date,
            'rrule': rrule,
            'amount': amount,
            'category': category,
            'notes': notes,
            'created_at': BaseModel.utc_now(),
            'updated_at': BaseModel.utc_now()
        }
        
        result = db.bills.insert_one(doc)
        doc['_id'] = result.inserted_id
        return BaseModel.serialize_doc(doc)
    
    @staticmethod
    def get_upcoming_bills(db, user_id, limit=20):
        """Get upcoming bills including expanded recurring bills"""
        from dateutil import rrule as dateutil_rrule
        from dateutil.parser import parse
        from datetime import timedelta
        
        now = BaseModel.utc_now()
        end_date = now + timedelta(days=365)  # Look ahead 1 year
        
        # Get all bills for the user
        bills = list(db.bills.find({'user_id': ObjectId(user_id)}))
        
        upcoming = []
        
        for bill in bills:
            if bill.get('rrule'):
                # Expand recurring bills
                try:
                    dtstart = BaseModel.ensure_utc_aware(bill['due_date'])
                    rule = dateutil_rrule.rrulestr(bill['rrule'], dtstart=dtstart)
                    instances = rule.between(now, end_date)
                    
                    for instance in instances[:12]:  # Max 12 instances per bill
                        bill_copy = bill.copy()
                        bill_copy['due_date'] = instance
                        bill_copy['is_recurring'] = True
                        upcoming.append(bill_copy)
                except Exception as e:
                    bill_id = bill.get('_id', 'unknown')
                    rrule_str = bill.get('rrule', 'none')
                    logger.warning(f"Skipping bill {bill_id} due to invalid RRULE '{rrule_str}': {e}")
            elif bill.get('due_date'):
                # One-time bill
                # Ensure due_date is timezone-aware before comparison
                due_date_aware = BaseModel.ensure_utc_aware(bill['due_date'])
                if due_date_aware >= now:
                    bill['is_recurring'] = False
                    upcoming.append(bill)
        
        # Sort by due date
        upcoming.sort(key=lambda x: x['due_date'])
        
        # Take only the requested limit
        upcoming = upcoming[:limit]
        
        return [BaseModel.serialize_doc(b) for b in upcoming]
    
    @staticmethod
    def get_bills_by_category(db, user_id):
        """Get bill amounts grouped by category"""
        pipeline = [
            {'$match': {'user_id': ObjectId(user_id), 'amount': {'$exists': True}}},
            {
                '$group': {
                    '_id': '$category',
                    'total': {'$sum': '$amount'},
                    'count': {'$sum': 1}
                }
            }
        ]
        
        results = list(db.bills.aggregate(pipeline))
        return results

class EventModel(BaseModel):
    """Event/Appointment model operations"""
    
    @staticmethod
    def create_event(db, user_id, title, start, end, rrule=None, notes=None):
        """Create a new event"""
        doc = {
            'user_id': ObjectId(user_id),
            'title': title,
            'start': start,
            'end': end,
            'rrule': rrule,
            'notes': notes,
            'created_at': BaseModel.utc_now(),
            'updated_at': BaseModel.utc_now()
        }
        
        result = db.events.insert_one(doc)
        doc['_id'] = result.inserted_id
        return BaseModel.serialize_doc(doc)
    
    @staticmethod
    def get_upcoming_events(db, user_id, limit=10):
        """Get upcoming events including expanded recurring events"""
        from dateutil import rrule as dateutil_rrule
        from datetime import timedelta
        
        now = BaseModel.utc_now()
        end_date = now + timedelta(days=90)  # Look ahead 90 days
        
        events = list(db.events.find({'user_id': ObjectId(user_id)}))
        
        upcoming = []
        
        for event in events:
            if event.get('rrule'):
                # Expand recurring events
                try:
                    rule = dateutil_rrule.rrulestr(event['rrule'], dtstart=event['start'])
                    instances = rule.between(now, end_date)
                    
                    for instance in instances[:5]:  # Max 5 instances per event
                        event_copy = event.copy()
                        # Ensure datetime fields are timezone-aware for arithmetic
                        end_aware = BaseModel.ensure_utc_aware(event['end'])
                        start_aware = BaseModel.ensure_utc_aware(event['start'])
                        duration = end_aware - start_aware
                        event_copy['start'] = instance
                        event_copy['end'] = instance + duration
                        event_copy['is_recurring'] = True
                        upcoming.append(event_copy)
                except Exception as e:
                    event_id = event.get('_id', 'unknown')
                    rrule_str = event.get('rrule', 'none')
                    logger.warning(f"Skipping event {event_id} due to invalid RRULE '{rrule_str}': {e}")
            else:
                # One-time event
                # Ensure start is timezone-aware before comparison
                start_aware = BaseModel.ensure_utc_aware(event['start'])
                if start_aware >= now:
                    event['is_recurring'] = False
                    upcoming.append(event)
        
        # Sort by start time
        upcoming.sort(key=lambda x: x['start'])
        
        # Take only the requested limit
        upcoming = upcoming[:limit]
        
        return [BaseModel.serialize_doc(e) for e in upcoming]

class TodoModel(BaseModel):
    """Todo model operations"""
    
    @staticmethod
    def create_todo(db, todo_data):
        """Create a new todo"""
        doc = {
            'user_id': todo_data['user_id'],
            'text': todo_data['text'],
            'created_at': BaseModel.utc_now()
        }
        
        result = db.todos.insert_one(doc)
        doc['_id'] = result.inserted_id
        return BaseModel.serialize_doc(doc)
    
    @staticmethod
    def get_user_todos(db, user_id, limit=None):
        """Get all todos for a user"""
        query = {'user_id': ObjectId(user_id)}
        cursor = db.todos.find(query).sort('created_at', -1)
        
        if limit:
            cursor = cursor.limit(limit)
        
        return [BaseModel.serialize_doc(t) for t in cursor]
    
    @staticmethod
    def delete_todo(db, todo_id, user_id):
        """Delete a todo (hard delete)"""
        result = db.todos.delete_one({
            '_id': ObjectId(todo_id),
            'user_id': ObjectId(user_id)
        })
        return result.deleted_count > 0
    
    @staticmethod
    def get_todo_by_id(db, todo_id, user_id):
        """Get a specific todo by ID and user"""
        todo = db.todos.find_one({
            '_id': ObjectId(todo_id),
            'user_id': ObjectId(user_id)
        })
        return BaseModel.serialize_doc(todo) if todo else None
    
    @staticmethod
    def delete_multiple_todos(db, todo_ids, user_id):
        """Delete multiple todos at once"""
        # Convert string IDs to ObjectIds
        object_ids = []
        for todo_id in todo_ids:
            try:
                object_ids.append(ObjectId(todo_id))
            except Exception as e:
                logger.warning(f"Invalid todo ID {todo_id}: {e}")
                continue
        
        if not object_ids:
            return 0
        
        result = db.todos.delete_many({
            '_id': {'$in': object_ids},
            'user_id': ObjectId(user_id)
        })
        return result.deleted_count
    
    @staticmethod
    def get_todos_count(db, user_id):
        """Get total count of todos for a user"""
        return db.todos.count_documents({'user_id': ObjectId(user_id)})

class WeatherModel(BaseModel):
    """Weather cache model operations"""
    
    @staticmethod
    def get_cached_weather(db, city):
        """Get cached weather data if fresh"""
        from datetime import timedelta
        
        cache_entry = db.weather_cache.find_one({'_id': city})
        
        if cache_entry:
            # Ensure fetched_at is timezone-aware before comparison
            fetched_at_aware = BaseModel.ensure_utc_aware(cache_entry.get('fetched_at'))
            age = BaseModel.utc_now() - fetched_at_aware
            if age < timedelta(hours=2):
                return cache_entry['payload']
        
        return None
    
    @staticmethod
    def cache_weather(db, city, weather_data):
        """Cache weather data"""
        doc = {
            '_id': city,
            'fetched_at': BaseModel.utc_now(),
            'payload': weather_data
        }
        
        db.weather_cache.replace_one(
            {'_id': city},
            doc,
            upsert=True
        )
        
        return weather_data