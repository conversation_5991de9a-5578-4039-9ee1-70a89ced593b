"""
Personal Organizer - Main Flask Application
A world-class personal life management system
"""

import os
import logging
from flask import Flask, jsonify, request
from flask_cors import CORS
from flask_pymongo import PyMongo
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from datetime import datetime

from config import get_config
from auth import init_auth
from routes import register_blueprints

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)

# Load configuration
config_obj = get_config()
app.config.from_object(config_obj)

# Initialize MongoDB
mongo = PyMongo(app)
app.mongo = mongo  # Make mongo accessible throughout the app

# Initialize CORS
CORS(app, 
     origins=[app.config['FRONTEND_URL']], 
     supports_credentials=True,
     allow_headers=['Content-Type', 'Authorization'],
     methods=['GET', 'POST', 'PATCH', 'DELETE', 'OPTIONS'])

# Initialize rate limiter
limiter = Limiter(
    app=app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"],
    storage_uri=app.config['RATELIMIT_STORAGE_URI']
)

# Initialize authentication
login_manager = init_auth(app, mongo)

# Register blueprints
register_blueprints(app)

# Error handlers
@app.errorhandler(400)
def bad_request(error):
    """Handle bad request errors"""
    return jsonify({
        'success': False,
        'message': 'Bad request',
        'error': str(error)
    }), 400

@app.errorhandler(401)
def unauthorized(error):
    """Handle unauthorized errors"""
    return jsonify({
        'success': False,
        'message': 'Authentication required'
    }), 401

@app.errorhandler(403)
def forbidden(error):
    """Handle forbidden errors"""
    return jsonify({
        'success': False,
        'message': 'Access forbidden'
    }), 403

@app.errorhandler(404)
def not_found(error):
    """Handle not found errors"""
    return jsonify({
        'success': False,
        'message': 'Resource not found'
    }), 404

@app.errorhandler(429)
def rate_limit_exceeded(error):
    """Handle rate limit errors"""
    return jsonify({
        'success': False,
        'message': 'Rate limit exceeded. Please try again later.'
    }), 429

@app.errorhandler(500)
def internal_error(error):
    """Handle internal server errors"""
    logger.error(f"Internal server error: {error}")
    return jsonify({
        'success': False,
        'message': 'An internal error occurred'
    }), 500

# Health check endpoint
@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint for monitoring"""
    try:
        # Check MongoDB connection
        mongo.db.command('ping')
        db_status = 'healthy'
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        db_status = 'unhealthy'
    
    return jsonify({
        'status': 'healthy' if db_status == 'healthy' else 'degraded',
        'timestamp': datetime.utcnow().isoformat(),
        'services': {
            'api': 'healthy',
            'database': db_status
        }
    }), 200 if db_status == 'healthy' else 503

# Root endpoint
@app.route('/', methods=['GET'])
def root():
    """Root endpoint"""
    return jsonify({
        'name': 'Personal Organizer API',
        'version': '1.0.0',
        'status': 'running'
    }), 200

# Setup database indexes
def setup_indexes():
    """Create MongoDB indexes for optimal performance"""
    try:
        # Users indexes
        mongo.db.users.create_index('username', unique=True)
        mongo.db.users.create_index('created_at')
        
        # Bills indexes
        mongo.db.bills.create_index([('user_id', 1), ('due_date', 1)])
        mongo.db.bills.create_index([('user_id', 1), ('category', 1)])
        
        # Events indexes
        mongo.db.events.create_index([('user_id', 1), ('start', 1)])
        
        # Contacts indexes
        mongo.db.contacts.create_index('user_id')
        mongo.db.contacts.create_index('type_id')
        mongo.db.contacts.create_index([('user_id', 1), ('last_contact', -1)])
        
        # Contact types indexes
        mongo.db.contact_types.create_index('user_id')
        
        # Todos indexes
        mongo.db.todos.create_index([('user_id', 1), ('created_at', -1)])
        
        # Weather cache indexes with TTL
        mongo.db.weather_cache.create_index(
            'fetched_at',
            expireAfterSeconds=app.config['WEATHER_CACHE_TTL_SECONDS']
        )
        
        # Audit log indexes
        mongo.db.audit_log.create_index([('user_id', 1), ('timestamp', -1)])
        
        logger.info("Database indexes created successfully")
        
    except Exception as e:
        logger.error(f"Error creating indexes: {e}")

# Initialize app context items
with app.app_context():
    setup_indexes()

# Custom JSON encoder for MongoDB ObjectId and datetime
from bson import ObjectId
import json

class MongoJSONEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, ObjectId):
            return str(o)
        if isinstance(o, datetime):
            return o.isoformat()
        return super().default(o)

app.json_encoder = MongoJSONEncoder

# Request logging middleware
@app.before_request
def log_request():
    """Log incoming requests"""
    if app.config.get('DEBUG'):
        logger.debug(f"{request.method} {request.path} - {request.remote_addr}")

@app.after_request
def log_response(response):
    """Log response status"""
    if app.config.get('DEBUG'):
        logger.debug(f"Response: {response.status_code}")
    return response

if __name__ == '__main__':
    # Development server only - use port 5001 to avoid conflicts
    port = int(os.environ.get('FLASK_PORT', 5001))
    app.run(
        host='127.0.0.1',
        port=port,
        debug=app.config.get('DEBUG', False)
    )