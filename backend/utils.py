"""
Utility functions for Personal Organizer
Handles timezone conversion, RRULE parsing, and weather API calls
"""

import requests
from datetime import datetime, timezone, timedelta
from dateutil import rrule
from dateutil.parser import parse
import pytz
import logging

logger = logging.getLogger(__name__)

# Timezone utilities
def utc_now():
    """Get current UTC time with timezone awareness"""
    return datetime.now(timezone.utc)

def to_chicago_time(dt):
    """Convert any datetime to Chicago timezone"""
    if dt is None:
        return None
    
    # Ensure datetime is timezone aware
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)
    
    chicago_tz = pytz.timezone('America/Chicago')
    return dt.astimezone(chicago_tz)

def from_chicago_time(dt):
    """Convert Chicago time to UTC"""
    if dt is None:
        return None
    
    chicago_tz = pytz.timezone('America/Chicago')
    
    # If naive datetime, assume it's in Chicago time
    if dt.tzinfo is None:
        dt = chicago_tz.localize(dt)
    
    return dt.astimezone(timezone.utc)

def format_datetime_display(dt):
    """Format datetime for display in Chicago timezone"""
    if dt is None:
        return ""
    
    chicago_dt = to_chicago_time(dt)
    return chicago_dt.strftime("%B %d, %Y at %I:%M %p")

def format_date_display(dt):
    """Format date for display in Chicago timezone"""
    if dt is None:
        return ""
    
    chicago_dt = to_chicago_time(dt)
    return chicago_dt.strftime("%B %d, %Y")

# RRULE utilities
def parse_rrule(rrule_string, dtstart=None):
    """Parse RRULE string and return rrule object"""
    try:
        if dtstart:
            return rrule.rrulestr(rrule_string, dtstart=dtstart)
        return rrule.rrulestr(rrule_string)
    except Exception as e:
        logger.error(f"Error parsing RRULE: {e}")
        return None

def expand_rrule(rrule_string, dtstart, until, max_instances=100):
    """Expand RRULE to get instances between dtstart and until"""
    try:
        rule = parse_rrule(rrule_string, dtstart)
        if not rule:
            return []
        
        instances = list(rule.between(dtstart, until))
        return instances[:max_instances]
    except Exception as e:
        logger.error(f"Error expanding RRULE: {e}")
        return []

def validate_rrule(rrule_string):
    """Validate RRULE string format"""
    try:
        rrule.rrulestr(rrule_string)
        return True, "Valid RRULE"
    except Exception as e:
        return False, str(e)

# Weather API utilities
def fetch_weather_data(city_config):
    """Fetch weather data from Open-Meteo API"""
    try:
        base_url = "https://api.open-meteo.com/v1/forecast"
        
        params = {
            'latitude': city_config['latitude'],
            'longitude': city_config['longitude'],
            'current': 'temperature_2m,weather_code',
            'daily': 'temperature_2m_max,temperature_2m_min,weather_code',
            'temperature_unit': 'fahrenheit',
            'forecast_days': 3,
            'timezone': 'auto'
        }
        
        response = requests.get(base_url, params=params, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        
        # Transform data to our format
        weather_data = {
            'current': {
                'temperature': round(data['current']['temperature_2m']),
                'condition': weather_code_to_condition(data['current']['weather_code']),
                'icon': weather_code_to_icon(data['current']['weather_code'])
            },
            'forecast': []
        }
        
        # Process 3-day forecast
        for i in range(3):
            # Parse date and ensure it's timezone-aware (UTC)
            forecast_date = parse(data['daily']['time'][i])
            if forecast_date.tzinfo is None:
                forecast_date = forecast_date.replace(tzinfo=timezone.utc)
                
            weather_data['forecast'].append({
                'date': forecast_date,
                'high': round(data['daily']['temperature_2m_max'][i]),
                'low': round(data['daily']['temperature_2m_min'][i]),
                'condition': weather_code_to_condition(data['daily']['weather_code'][i]),
                'icon': weather_code_to_icon(data['daily']['weather_code'][i])
            })
        
        return weather_data
        
    except requests.RequestException as e:
        logger.error(f"Error fetching weather data: {e}")
        return None
    except Exception as e:
        logger.error(f"Error processing weather data: {e}")
        return None

def weather_code_to_condition(code):
    """Convert Open-Meteo weather code to condition string"""
    weather_conditions = {
        0: "Clear",
        1: "Mainly Clear",
        2: "Partly Cloudy",
        3: "Overcast",
        45: "Foggy",
        48: "Depositing Rime Fog",
        51: "Light Drizzle",
        53: "Moderate Drizzle",
        55: "Dense Drizzle",
        61: "Slight Rain",
        63: "Moderate Rain",
        65: "Heavy Rain",
        71: "Slight Snow",
        73: "Moderate Snow",
        75: "Heavy Snow",
        80: "Slight Rain Showers",
        81: "Moderate Rain Showers",
        82: "Violent Rain Showers",
        95: "Thunderstorm",
        96: "Thunderstorm with Hail",
        99: "Thunderstorm with Heavy Hail"
    }
    
    return weather_conditions.get(code, "Unknown")

def weather_code_to_icon(code):
    """Convert Open-Meteo weather code to icon identifier"""
    # Map weather codes to simple icon identifiers
    # Frontend will map these to actual icons
    icon_map = {
        0: "sun",
        1: "sun",
        2: "cloud-sun",
        3: "cloud",
        45: "cloud-fog",
        48: "cloud-fog",
        51: "cloud-drizzle",
        53: "cloud-drizzle",
        55: "cloud-drizzle",
        61: "cloud-rain",
        63: "cloud-rain",
        65: "cloud-rain",
        71: "cloud-snow",
        73: "cloud-snow",
        75: "cloud-snow",
        80: "cloud-rain",
        81: "cloud-rain",
        82: "cloud-rain",
        95: "cloud-lightning",
        96: "cloud-lightning",
        99: "cloud-lightning"
    }
    
    return icon_map.get(code, "help-circle")

# Data processing utilities
def calculate_contact_status(last_contact, threshold_days):
    """Calculate contact status color based on threshold"""
    if not last_contact:
        return 'red', None
    
    days_since = (utc_now() - last_contact).days
    
    if days_since <= threshold_days:
        return 'green', days_since
    elif days_since <= threshold_days * 2:
        return 'yellow', days_since
    else:
        return 'red', days_since

def aggregate_bills_by_category(bills):
    """Aggregate bill amounts by category"""
    category_totals = {}
    
    for bill in bills:
        if bill.get('amount') and bill.get('category'):
            category = bill['category']
            if category not in category_totals:
                category_totals[category] = {
                    'total': 0,
                    'count': 0
                }
            category_totals[category]['total'] += bill['amount']
            category_totals[category]['count'] += 1
    
    return category_totals

# Validation utilities
def validate_email(email):
    """Basic email validation"""
    import re
    
    if not email:
        return True  # Email is optional
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_phone(phone):
    """Basic phone validation"""
    import re
    
    if not phone:
        return True  # Phone is optional
    
    # Remove common formatting characters
    cleaned = re.sub(r'[\s\-\(\)\+]', '', phone)
    
    # Check if it's all digits and reasonable length
    return cleaned.isdigit() and 10 <= len(cleaned) <= 15

def sanitize_input(text):
    """Sanitize user input to prevent XSS using nh3 library"""
    if not text:
        return text
    
    try:
        import nh3
        # Use nh3 (modern replacement for deprecated bleach)
        # Clean text with no allowed tags - escape all HTML
        return nh3.clean(text, tags=set(), attributes={})
    except ImportError:
        logger.error("nh3 library not installed, falling back to basic sanitization")
        # Fallback to basic sanitization if nh3 not available
        replacements = {
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#x27;',
            '/': '&#x2F;',
            '&': '&amp;'
        }
        
        for old, new in replacements.items():
            text = text.replace(old, new)
        
        return text

# Logging utilities
def log_user_action(mongo, user_id, action, details=None):
    """Log user actions for audit trail"""
    # Extract IP address from details if provided
    ip_address = details.get('ip') if isinstance(details, dict) and details else None
    
    log_entry = {
        'user_id': user_id,
        'action': action,
        'details': details,
        'timestamp': utc_now(),
        'ip_address': ip_address
    }
    
    try:
        mongo.db.audit_log.insert_one(log_entry)
    except Exception as e:
        logger.error(f"Error logging user action: {e}")

# Response utilities
def success_response(data=None, message="Success", status_code=200):
    """Create standardized success response"""
    response = {
        'success': True,
        'message': message
    }
    
    if data is not None:
        response['data'] = data
    
    return response, status_code

def error_response(message="An error occurred", status_code=400, errors=None):
    """Create standardized error response"""
    response = {
        'success': False,
        'message': message
    }
    
    if errors:
        response['errors'] = errors
    
    return response, status_code