"""
Configuration management for Personal Organizer
Handles environment variables and application settings
"""

import os
from datetime import timedelta
from decouple import config

class Config:
    """Base configuration class"""
    
    # Flask Core
    SECRET_KEY = config('SECRET_KEY', default='dev-secret-key-change-in-production')
    FLASK_ENV = config('FLASK_ENV', default='production')
    DEBUG = config('FLASK_DEBUG', default=False, cast=bool)
    
    # MongoDB
    MONGO_URI = config('MONGO_URI', default='mongodb://localhost:27017/personal_organizer')
    
    # Session Configuration
    SESSION_COOKIE_SECURE = config('SESSION_COOKIE_SECURE', default=True, cast=bool)
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = timedelta(days=7)
    
    # CORS Configuration
    FRONTEND_URL = config('FRONTEND_URL', default='http://localhost:5173')
    
    # Rate Limiting
    RATELIMIT_STORAGE_URI = config('REDIS_URI', default='memory://')
    RATELIMIT_DEFAULT = "200 per day;50 per hour"
    RATELIMIT_HEADERS_ENABLED = True
    
    # Weather API Configuration
    WEATHER_API_URL = config('WEATHER_API_URL', default='https://api.open-meteo.com/v1/forecast')
    WEATHER_CACHE_TTL = 7200  # 2 hours in seconds
    
    # Weather Cities Configuration
    WEATHER_CITIES = {
        'stl': {
            'name': 'St. Louis',
            'latitude': config('STL_LAT', default=38.6270, cast=float),
            'longitude': config('STL_LON', default=-90.1994, cast=float)
        },
        'accra': {
            'name': 'Accra',
            'latitude': config('ACCRA_LAT', default=5.6037, cast=float),
            'longitude': config('ACCRA_LON', default=-0.1870, cast=float)
        }
    }
    
    # Timezone Configuration
    DEFAULT_TIMEZONE = config('DEFAULT_TIMEZONE', default='America/Chicago')
    
    # Admin User Configuration (for initial setup)
    ADMIN_USERNAME = config('ADMIN_USERNAME', default='admin')
    ADMIN_PASSWORD = config('ADMIN_PASSWORD', default='change-this-password')
    
    # Application Limits
    MAX_TODOS_DISPLAY = 300  # Before virtualization kicks in
    MAX_BILL_INSTANCES = 12  # Months of recurring bills to expand
    MAX_LOGIN_ATTEMPTS = 5   # Per minute
    
    # TTL Settings (in seconds)
    WEATHER_CACHE_TTL_SECONDS = 7200      # 2 hours
    TODO_COMPLETED_TTL_SECONDS = 86400    # 24 hours
    BILL_INSTANCE_TTL_MONTHS = 24         # 24 months
    
    # Dashboard Layout Defaults
    DEFAULT_DASHBOARD_LAYOUT = {
        'lg': [
            {'i': 'weather-current', 'x': 0, 'y': 0, 'w': 3, 'h': 4},
            {'i': 'weather-forecast', 'x': 3, 'y': 0, 'w': 3, 'h': 4},
            {'i': 'bills-upcoming', 'x': 6, 'y': 0, 'w': 4, 'h': 6},
            {'i': 'events-upcoming', 'x': 10, 'y': 0, 'w': 2, 'h': 6},
            {'i': 'bills-chart', 'x': 0, 'y': 4, 'w': 3, 'h': 4},
            {'i': 'todos', 'x': 3, 'y': 4, 'w': 3, 'h': 4},
            {'i': 'contacts', 'x': 0, 'y': 8, 'w': 6, 'h': 4}
        ],
        'md': [
            {'i': 'weather-current', 'x': 0, 'y': 0, 'w': 3, 'h': 3},
            {'i': 'weather-forecast', 'x': 3, 'y': 0, 'w': 3, 'h': 3},
            {'i': 'bills-upcoming', 'x': 6, 'y': 0, 'w': 4, 'h': 5},
            {'i': 'events-upcoming', 'x': 0, 'y': 3, 'w': 3, 'h': 5},
            {'i': 'bills-chart', 'x': 3, 'y': 3, 'w': 3, 'h': 5},
            {'i': 'todos', 'x': 6, 'y': 5, 'w': 4, 'h': 4},
            {'i': 'contacts', 'x': 0, 'y': 8, 'w': 6, 'h': 4}
        ],
        'sm': [
            {'i': 'weather-current', 'x': 0, 'y': 0, 'w': 3, 'h': 3},
            {'i': 'weather-forecast', 'x': 3, 'y': 0, 'w': 3, 'h': 3},
            {'i': 'bills-upcoming', 'x': 0, 'y': 3, 'w': 6, 'h': 4},
            {'i': 'events-upcoming', 'x': 0, 'y': 7, 'w': 3, 'h': 4},
            {'i': 'bills-chart', 'x': 3, 'y': 7, 'w': 3, 'h': 4},
            {'i': 'todos', 'x': 0, 'y': 11, 'w': 6, 'h': 4},
            {'i': 'contacts', 'x': 0, 'y': 15, 'w': 6, 'h': 4}
        ],
        'xs': [
            {'i': 'weather-current', 'x': 0, 'y': 0, 'w': 4, 'h': 3},
            {'i': 'weather-forecast', 'x': 0, 'y': 3, 'w': 4, 'h': 3},
            {'i': 'bills-upcoming', 'x': 0, 'y': 6, 'w': 4, 'h': 4},
            {'i': 'events-upcoming', 'x': 0, 'y': 10, 'w': 4, 'h': 4},
            {'i': 'bills-chart', 'x': 0, 'y': 14, 'w': 4, 'h': 4},
            {'i': 'todos', 'x': 0, 'y': 18, 'w': 4, 'h': 4},
            {'i': 'contacts', 'x': 0, 'y': 22, 'w': 4, 'h': 4}
        ]
    }
    
    # Bill Categories Defaults
    DEFAULT_CATEGORIES = [
        {'id': 'mortgage', 'name': 'Mortgage', 'color': '#3B82F6'},
        {'id': 'utilities', 'name': 'Utilities', 'color': '#10B981'},
        {'id': 'insurance', 'name': 'Insurance', 'color': '#F59E0B'},
        {'id': 'car', 'name': 'Car Payment', 'color': '#EF4444'},
        {'id': 'vacation', 'name': 'Vacation', 'color': '#8B5CF6'},
        {'id': 'other', 'name': 'Other', 'color': '#6B7280'}
    ]
    
    # Contact Type Defaults
    DEFAULT_CONTACT_TYPES = [
        {'label': 'Family', 'threshold_days': 7},
        {'label': 'Close Friends', 'threshold_days': 14},
        {'label': 'Friends', 'threshold_days': 21},
        {'label': 'Business', 'threshold_days': 30}
    ]

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    SESSION_COOKIE_SECURE = False
    
class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    SESSION_COOKIE_SECURE = True

# Configuration dictionary
config_dict = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': ProductionConfig
}

def get_config():
    """Get configuration based on environment"""
    env = config('FLASK_ENV', default='production')
    return config_dict.get(env, config_dict['default'])