[Unit]
Description=Personal Organizer - Life Management Application
Documentation=https://github.com/yourusername/personal-organizer
After=network.target mongodb.service
Wants=mongodb.service

[Service]
Type=notify
User=www-data
Group=www-data
WorkingDirectory=/opt/personal-organizer/backend
Environment="PATH=/opt/personal-organizer/venv/bin"
Environment="PYTHONPATH=/opt/personal-organizer/backend"
EnvironmentFile=/opt/personal-organizer/.env

# Start command
ExecStart=/opt/personal-organizer/venv/bin/gunicorn \
    --workers 4 \
    --worker-class sync \
    --bind 127.0.0.1:5000 \
    --timeout 120 \
    --access-logfile /var/log/personal-organizer/access.log \
    --error-logfile /var/log/personal-organizer/error.log \
    --capture-output \
    --enable-stdio-inheritance \
    wsgi:application

# Restart policy
Restart=always
RestartSec=10

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/personal-organizer

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Kill settings
KillMode=mixed
KillSignal=SIGTERM

[Install]
WantedBy=multi-user.target