# Personal Organizer

A world-class personal life management system with Apple-level design, built for self-hosting on your Ubuntu homelab.

![Personal Organizer](https://img.shields.io/badge/version-1.0.0-blue.svg)
![Python](https://img.shields.io/badge/python-3.x-blue.svg)
![React](https://img.shields.io/badge/react-18.x-blue.svg)
![MongoDB](https://img.shields.io/badge/mongodb-latest-green.svg)

## Features

- **📊 Drag-and-Drop Dashboard** - Customizable grid layout with resizable widgets
- **💵 Bills & Vacation Tracking** - Manage recurring bills and track upcoming trips
- **📅 Event Management** - Schedule and track appointments with recurrence support
- **👥 Contact Reminders** - Color-coded system to maintain relationships
- **✅ Unlimited Todos** - Fast, searchable todo list with virtualization
- **🌤️ Weather Integration** - Real-time weather for St. Louis and Accra
- **🎨 Beautiful UI** - Glass morphism design with smooth animations
- **🌓 Dark Mode** - Automatic theme switching with system preference detection
- **🔒 Secure** - Single-user authentication with rate limiting

## Tech Stack

### Backend
- **Python 3.x** with Flask
- **MongoDB** for data storage
- **Flask-Login** for authentication
- **Flask-Limiter** for rate limiting
- **Gunicorn** for production serving

### Frontend
- **React 18** with functional components
- **Vite** for blazing fast builds
- **Tailwind CSS** for styling
- **React Grid Layout** for dashboard
- **Framer Motion** for animations
- **React Query** for data fetching
- **Recharts** for data visualization

## Prerequisites

- Ubuntu 20.04+ (or similar Linux distribution)
- Python 3.8+
- Node.js 18+
- MongoDB 4.4+
- Nginx (for reverse proxy)
- Cloudflare account (for tunnel)

## Installation

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/personal-organizer.git
cd personal-organizer
```

### 2. Backend Setup

```bash
# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
cd backend
pip install -r requirements.txt

# Copy environment variables
cp .env.example .env

# Edit .env with your settings
nano .env
```

### 3. Frontend Setup

```bash
# Install dependencies
cd ../frontend
npm install

# Build for production
npm run build
```

### 4. MongoDB Setup

```bash
# Install MongoDB if not already installed
sudo apt update
sudo apt install mongodb-org

# Start MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod

# Create database and indexes (automatic on first run)
```

### 5. Environment Configuration

Edit the `.env` file with your specific settings:

```env
SECRET_KEY=generate-a-secure-secret-key
MONGO_URI=mongodb://localhost:27017/personal_organizer
FRONTEND_URL=https://your-domain.com
ADMIN_USERNAME=your-username
ADMIN_PASSWORD=change-this-immediately
```

## Running the Application

### Development Mode

```bash
# Terminal 1 - Backend
cd backend
source venv/bin/activate
python app.py

# Terminal 2 - Frontend
cd frontend
npm run dev
```

### Production Deployment

1. **Install as systemd service:**

```bash
# Copy service file
sudo cp personal-organizer.service /etc/systemd/system/

# Edit paths in service file
sudo nano /etc/systemd/system/personal-organizer.service

# Enable and start service
sudo systemctl enable personal-organizer
sudo systemctl start personal-organizer
```

2. **Set up Nginx reverse proxy:**

```nginx
server {
    server_name your-domain.com;
    
    location / {
        root /opt/personal-organizer/frontend/dist;
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

3. **Configure Cloudflare Tunnel:**

```bash
# Install cloudflared
wget -q https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64.deb
sudo dpkg -i cloudflared-linux-amd64.deb

# Authenticate
cloudflared tunnel login

# Create tunnel
cloudflared tunnel create personal-organizer

# Configure tunnel
cloudflared tunnel route dns personal-organizer your-domain.com

# Run tunnel
cloudflared tunnel run personal-organizer
```

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/check` - Check auth status

### Dashboard
- `GET /api/dashboard` - Get all dashboard data
- `POST /api/dashboard/layout` - Save layout

### Bills
- `GET /api/bills` - Get all bills
- `POST /api/bills` - Create bill
- `PATCH /api/bills/:id` - Update bill
- `DELETE /api/bills/:id` - Delete bill

### Events
- `GET /api/events` - Get all events
- `POST /api/events` - Create event
- `PATCH /api/events/:id` - Update event
- `DELETE /api/events/:id` - Delete event

### Contacts
- `GET /api/contacts` - Get all contacts
- `POST /api/contacts` - Create contact
- `PATCH /api/contacts/:id` - Update contact
- `DELETE /api/contacts/:id` - Delete contact
- `POST /api/contacts/:id/ping` - Update last contact

### Todos
- `GET /api/todos` - Get all todos
- `POST /api/todos` - Create todo
- `DELETE /api/todos/:id` - Complete todo

### Weather
- `GET /api/weather?city=stl` - Get weather data

### User
- `GET /api/user` - Get profile
- `PATCH /api/user` - Update profile

## Data Backup

### Automated Backups

Create a backup script `/opt/scripts/backup-personal-organizer.sh`:

```bash
#!/bin/bash
BACKUP_DIR="/backup/personal-organizer"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup MongoDB
mongodump --db personal_organizer --out $BACKUP_DIR/mongodb_$DATE

# Compress backup
tar -czf $BACKUP_DIR/backup_$DATE.tar.gz $BACKUP_DIR/mongodb_$DATE

# Remove uncompressed backup
rm -rf $BACKUP_DIR/mongodb_$DATE

# Keep only last 30 days of backups
find $BACKUP_DIR -name "backup_*.tar.gz" -mtime +30 -delete
```

Add to crontab:
```bash
0 2 * * * /opt/scripts/backup-personal-organizer.sh
```

### Manual Restore

```bash
# Extract backup
tar -xzf /backup/personal-organizer/backup_20240101_020000.tar.gz

# Restore MongoDB
mongorestore --db personal_organizer backup_20240101_020000/personal_organizer
```

## Monitoring

### Check service status
```bash
sudo systemctl status personal-organizer
```

### View logs
```bash
# Service logs
sudo journalctl -u personal-organizer -f

# Application logs
tail -f /var/log/personal-organizer/error.log
tail -f /var/log/personal-organizer/access.log
```

### MongoDB status
```bash
mongo --eval "db.stats()"
```

## Troubleshooting

### Application won't start
- Check logs: `sudo journalctl -u personal-organizer -n 50`
- Verify MongoDB is running: `sudo systemctl status mongod`
- Check port availability: `sudo lsof -i :5000`

### Can't access from outside
- Verify Cloudflare Tunnel is running
- Check Nginx configuration
- Ensure firewall allows outbound connections

### High memory usage
- Check MongoDB indexes: `mongo personal_organizer --eval "db.getCollectionNames().forEach(function(c){print(c);printjson(db[c].getIndexes())})"`
- Verify TTL indexes are working
- Check for excessive todos or bills

## Performance Optimization

1. **Enable MongoDB compression:**
```javascript
use personal_organizer
db.runCommand({
    collMod: "bills",
    storageEngine: { wiredTiger: { configString: "block_compressor=zstd" } }
})
```

2. **Optimize Nginx:**
```nginx
# Enable gzip
gzip on;
gzip_types text/plain text/css application/json application/javascript;

# Cache static assets
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

3. **Frontend build optimization:**
```bash
# Analyze bundle size
npm run build -- --analyze

# Enable compression
npm install -D vite-plugin-compression
```

## Security Hardening

1. **Enable fail2ban:**
```bash
sudo apt install fail2ban
# Configure for Flask-Limiter logs
```

2. **Set up SSL with Let's Encrypt:**
```bash
sudo certbot --nginx -d your-domain.com
```

3. **Restrict MongoDB:**
```bash
# Edit /etc/mongod.conf
net:
  bindIp: 127.0.0.1
```

## Contributing

This is a personal project, but suggestions are welcome! Please open an issue to discuss changes.

## License

This project is private and not licensed for redistribution.

## Support

For issues or questions:
1. Check the logs first
2. Review the troubleshooting section
3. Open an issue with detailed information

---

Built with ❤️ for personal productivity