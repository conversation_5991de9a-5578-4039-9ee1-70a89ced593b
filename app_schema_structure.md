# Personal Organizer - Database Schema & Directory Structure

## Directory Structure

```
personal-organizer/
├── backend/
│   ├── app.py                    # Main Flask application
│   ├── wsgi.py                   # Gunicorn entry point
│   ├── config.py                 # Configuration management
│   ├── models.py                 # MongoDB models
│   ├── auth.py                   # Authentication logic
│   ├── routes.py                 # All API routes
│   ├── utils.py                  # Helper functions
│   └── requirements.txt          # Python dependencies
├── frontend/
│   ├── src/
│   │   ├── App.jsx              # Main React component
│   │   ├── main.jsx             # React entry point
│   │   ├── index.css            # Global styles
│   │   ├── api/                 # API client modules
│   │   │   └── client.js        # Axios configuration
│   │   ├── components/          # React components
│   │   │   ├── LoginPage.jsx    # Login screen
│   │   │   ├── Dashboard.jsx    # Main dashboard
│   │   │   ├── Profile.jsx      # Profile management
│   │   │   └── widgets/         # Dashboard widgets
│   │   │       ├── WeatherWidget.jsx
│   │   │       ├── BillsWidget.jsx
│   │   │       ├── EventsWidget.jsx
│   │   │       ├── ContactsWidget.jsx
│   │   │       ├── TodosWidget.jsx
│   │   │       └── ChartWidget.jsx
│   │   └── utils/               # Utility functions
│   │       └── helpers.js       # Date formatting, etc.
│   ├── public/
│   │   ├── index.html
│   │   └── favicon.svg
│   ├── package.json
│   ├── vite.config.js
│   ├── tailwind.config.js
│   └── postcss.config.js
├── .env.example                 # Environment variables template
├── .gitignore
├── README.md                    # Setup instructions
└── personal-organizer.service   # systemd service file
```

## MongoDB Database Schema

### Database: `personal_organizer`

### Collections:

#### 1. `users`
```javascript
{
  _id: ObjectId,
  username: String,              // Unique, required
  password_hash: String,         // PBKDF2 hash, required
  email: String,                 // Optional
  phone: String,                 // Optional
  dashboard_layout: {
    layouts: {
      lg: Array,                 // Layout for large screens
      md: Array,                 // Layout for medium screens
      sm: Array,                 // Layout for small screens
      xs: Array                  // Layout for mobile
    }
  },
  categories: [{
    id: String,                  // UUID
    name: String,                // e.g., "Utilities", "Insurance"
    color: String                // Hex color code
  }],
  theme: String,                 // 'light' or 'dark', default: 'light'
  created_at: Date,              // UTC timestamp
  updated_at: Date               // UTC timestamp
}

Indexes:
- username: unique
- created_at: 1
```

#### 2. `contact_types`
```javascript
{
  _id: ObjectId,
  user_id: ObjectId,             // Reference to users._id
  label: String,                 // e.g., "Family", "Friends"
  threshold_days: Number,        // 3, 7, or 21
  created_at: Date               // UTC timestamp
}

Indexes:
- user_id: 1
```

#### 3. `contacts`
```javascript
{
  _id: ObjectId,
  user_id: ObjectId,             // Reference to users._id
  type_id: ObjectId,             // Reference to contact_types._id
  name: String,                  // Contact name
  last_contact: Date,            // UTC timestamp
  notes: String,                 // Optional notes
  created_at: Date,              // UTC timestamp
  updated_at: Date               // UTC timestamp
}

Indexes:
- user_id: 1
- type_id: 1
- last_contact: -1
```

#### 4. `bills`
```javascript
{
  _id: ObjectId,
  user_id: ObjectId,             // Reference to users._id
  title: String,                 // e.g., "Mortgage", "Hawaii Trip"
  due_date: Date,                // UTC timestamp (null for recurring)
  rrule: String,                 // RFC 5545 RRULE (null for one-time)
  amount: Number,                // Optional amount
  category: String,              // Category ID
  notes: String,                 // Optional notes
  created_at: Date,              // UTC timestamp
  updated_at: Date               // UTC timestamp
}

Indexes:
- user_id: 1
- due_date: 1
- category: 1
```

#### 5. `events`
```javascript
{
  _id: ObjectId,
  user_id: ObjectId,             // Reference to users._id
  title: String,                 // Event title
  start: Date,                   // UTC timestamp
  end: Date,                     // UTC timestamp
  rrule: String,                 // RFC 5545 RRULE (null for one-time)
  notes: String,                 // Optional notes
  created_at: Date,              // UTC timestamp
  updated_at: Date               // UTC timestamp
}

Indexes:
- user_id: 1
- start: 1
```

#### 6. `todos`
```javascript
{
  _id: ObjectId,
  user_id: ObjectId,             // Reference to users._id
  text: String,                  // Todo text
  created_at: Date               // UTC timestamp
}

Indexes:
- user_id: 1
- created_at: -1
TTL: created_at + 24 hours (for completed todos)
```

#### 7. `weather_cache`
```javascript
{
  _id: String,                   // City code: "stl" or "accra"
  fetched_at: Date,              // UTC timestamp
  payload: {
    current: {
      temperature: Number,       // Fahrenheit
      condition: String,         // Weather condition
      icon: String              // Weather icon code
    },
    forecast: [{
      date: Date,
      high: Number,             // Fahrenheit
      low: Number,              // Fahrenheit
      condition: String,
      icon: String
    }]
  }
}

Indexes:
- fetched_at: 1
TTL: fetched_at + 2 hours
```

## API Endpoints Summary

- `POST /api/auth/login` - User login (rate limited: 5/min)
- `POST /api/auth/logout` - User logout
- `GET /api/auth/check` - Check auth status
- `GET /api/dashboard` - Get all dashboard data
- `GET/PATCH /api/user` - User profile management
- `GET/POST/PATCH/DELETE /api/bills` - Bills CRUD
- `GET/POST/PATCH/DELETE /api/events` - Events CRUD
- `GET/POST/PATCH/DELETE /api/contacts` - Contacts CRUD
- `GET/POST/DELETE /api/todos` - Todos management
- `GET /api/weather` - Weather data proxy
- `POST /api/layout` - Save dashboard layout

## Key Design Decisions

1. **Single User System**: No user registration, one admin user
2. **Time Zones**: All dates stored in UTC, displayed in America/Chicago
3. **Weather Cities**: St. Louis (stl) and Accra (accra) only
4. **Recurrence**: Using RFC 5545 RRULE standard
5. **TTL Indexes**: Auto-cleanup for weather cache and completed todos
6. **Categories**: User-defined with custom colors
7. **Contact Thresholds**: Fixed at 3, 7, or 21 days