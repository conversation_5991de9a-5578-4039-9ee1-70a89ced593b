<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Personal Organizer - A world-class life management system with Apple-level design" />
    <meta name="theme-color" content="#3b82f6" />
    
    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- System fonts fallback -->
    <style>
      /* Prevent FOUC (Flash of Unstyled Content) */
      html {
        visibility: hidden;
        opacity: 0;
      }
      
      /* Dark mode FOUC prevention */
      html.dark {
        background-color: #111827;
      }
    </style>
    
    <!-- Theme detection and application -->
    <script>
      // Immediately apply theme to prevent flash
      (function() {
        const theme = localStorage.getItem('theme') || 'light';
        document.documentElement.classList.toggle('dark', theme === 'dark');
        
        // Make visible after theme is set
        document.documentElement.style.visibility = 'visible';
        document.documentElement.style.opacity = '1';
      })();
    </script>
    
    <title>Personal Organizer</title>
  </head>
  <body>
    <noscript>
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        text-align: center;
        padding: 20px;
      ">
        <div>
          <h1 style="font-size: 24px; margin-bottom: 16px; color: #111827;">
            JavaScript Required
          </h1>
          <p style="color: #6b7280; max-width: 400px;">
            Personal Organizer requires JavaScript to run. Please enable JavaScript in your browser settings and refresh the page.
          </p>
        </div>
      </div>
    </noscript>
    
    <div id="root"></div>
    
    <!-- Loading indicator -->
    <div id="app-loader" style="
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: white;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: opacity 0.3s ease-out;
      z-index: 9999;
    ">
      <div style="text-align: center;">
        <div style="
          width: 40px;
          height: 40px;
          border: 3px solid #e5e7eb;
          border-top-color: #3b82f6;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 16px;
        "></div>
        <p style="
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          color: #6b7280;
          font-size: 14px;
        ">Loading Personal Organizer...</p>
      </div>
    </div>
    
    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Dark mode loader */
      html.dark #app-loader {
        background: #111827;
      }
      
      html.dark #app-loader div {
        border-color: #374151;
        border-top-color: #3b82f6;
      }
      
      html.dark #app-loader p {
        color: #9ca3af;
      }
    </style>
    
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>