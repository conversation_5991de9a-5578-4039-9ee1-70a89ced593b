import React, { useEffect } from 'react'
import { Routes, Route, Navigate, useLocation } from 'react-router-dom'
import { useQuery } from 'react-query'
import { motion, AnimatePresence } from 'framer-motion'
import toast from 'react-hot-toast'

// Components
import LoginPage from './components/LoginPage'
import Dashboard from './components/Dashboard'
import ProfilePage from './components/ProfilePage'
import Header from './components/Header'

// Hooks and utilities
import { useAuthStore } from './utils/useAuth'
import { useThemeStore } from './utils/useTheme'
import { checkAuth } from './api/auth'

// Loading component
const LoadingScreen = () => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="text-center"
    >
      <div className="w-16 h-16 border-4 border-primary-200 dark:border-primary-800 border-t-primary-600 dark:border-t-primary-400 rounded-full animate-spin mx-auto mb-4"></div>
      <p className="text-gray-600 dark:text-gray-400">Loading your personal organizer...</p>
    </motion.div>
  </div>
)

// Protected route wrapper
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, user } = useAuthStore()
  const location = useLocation()

  if (!isAuthenticated || !user) {
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  return children
}

// Page transition wrapper
const PageTransition = ({ children }) => (
  <motion.div
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -10 }}
    transition={{ duration: 0.2 }}
    className="min-h-screen"
  >
    {children}
  </motion.div>
)

function App() {
  const { setUser, setAuthenticated, logout } = useAuthStore()
  const { theme, initTheme } = useThemeStore()
  const location = useLocation()

  // Initialize theme on mount
  useEffect(() => {
    initTheme()
  }, [initTheme])

  // Check authentication status
  const { data: authData, isLoading, error } = useQuery(
    'auth-check',
    checkAuth,
    {
      retry: 1,
      staleTime: Infinity,
      cacheTime: Infinity,
      onSuccess: (data) => {
        if (data.authenticated && data.user) {
          setUser(data.user)
          setAuthenticated(true)
        } else {
          setUser(null)
          setAuthenticated(false)
        }
      },
      onError: (error) => {
        console.error('Auth check failed:', error)
        setUser(null)
        setAuthenticated(false)
      },
    }
  )

  // Handle authentication errors
  useEffect(() => {
    if (error?.response?.status === 401) {
      logout()
      toast.error('Session expired. Please login again.')
    }
  }, [error, logout])

  // Apply theme class to document
  useEffect(() => {
    document.documentElement.classList.toggle('dark', theme === 'dark')
  }, [theme])

  // Show loading screen while checking auth
  if (isLoading) {
    return <LoadingScreen />
  }

  const isAuthenticated = authData?.authenticated && authData?.user

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors">
      {/* Show header only when authenticated */}
      {isAuthenticated && <Header />}
      
      <AnimatePresence mode="wait">
        <Routes location={location} key={location.pathname}>
          {/* Public routes */}
          <Route
            path="/login"
            element={
              isAuthenticated ? (
                <Navigate to="/" replace />
              ) : (
                <PageTransition>
                  <LoginPage />
                </PageTransition>
              )
            }
          />

          {/* Protected routes */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <PageTransition>
                  <Dashboard />
                </PageTransition>
              </ProtectedRoute>
            }
          />
          
          <Route
            path="/profile"
            element={
              <ProtectedRoute>
                <PageTransition>
                  <ProfilePage />
                </PageTransition>
              </ProtectedRoute>
            }
          />

          {/* Catch all - redirect to dashboard or login */}
          <Route
            path="*"
            element={
              <Navigate to={isAuthenticated ? "/" : "/login"} replace />
            }
          />
        </Routes>
      </AnimatePresence>

      {/* Global styles for animations and transitions */}
      <style jsx global>{`
        /* Smooth scrolling */
        html {
          scroll-behavior: smooth;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }

        ::-webkit-scrollbar-track {
          background: transparent;
        }

        ::-webkit-scrollbar-thumb {
          background: #d1d5db;
          border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
          background: #9ca3af;
        }

        .dark ::-webkit-scrollbar-thumb {
          background: #4b5563;
        }

        .dark ::-webkit-scrollbar-thumb:hover {
          background: #6b7280;
        }

        /* Prevent text selection on UI elements */
        .no-select {
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
        }

        /* Glass morphism base */
        .glass {
          background: rgba(255, 255, 255, 0.25);
          backdrop-filter: blur(10px);
          -webkit-backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.18);
        }

        .dark .glass {
          background: rgba(17, 24, 39, 0.25);
          border: 1px solid rgba(255, 255, 255, 0.08);
        }

        /* Focus styles */
        *:focus {
          outline: none;
        }

        *:focus-visible {
          outline: 2px solid #3b82f6;
          outline-offset: 2px;
        }

        /* Loading animation */
        @keyframes pulse-subtle {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: 0.8;
          }
        }

        .animate-pulse-subtle {
          animation: pulse-subtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
      `}</style>
    </div>
  )
}

export default App