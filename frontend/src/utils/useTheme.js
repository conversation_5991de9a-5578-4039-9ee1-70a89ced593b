import React from 'react'
import { create } from 'zustand'
import { persist } from 'zustand/middleware'

// Theme store using Zustand with persistence
export const useThemeStore = create(
  persist(
    (set, get) => ({
      // State
      theme: 'light', // 'light' or 'dark'
      
      // Actions
      setTheme: (theme) => {
        set({ theme })
        // Apply theme to document
        applyTheme(theme)
      },
      
      toggleTheme: () => {
        const currentTheme = get().theme
        const newTheme = currentTheme === 'light' ? 'dark' : 'light'
        get().setTheme(newTheme)
      },
      
      initTheme: () => {
        const storedTheme = get().theme
        applyTheme(storedTheme)
      },
    }),
    {
      name: 'theme-storage',
    }
  )
)

// Apply theme to document
const applyTheme = (theme) => {
  const root = document.documentElement
  
  if (theme === 'dark') {
    root.classList.add('dark')
  } else {
    root.classList.remove('dark')
  }
  
  // Update meta theme color
  const metaThemeColor = document.querySelector('meta[name="theme-color"]')
  if (metaThemeColor) {
    metaThemeColor.content = theme === 'dark' ? '#111827' : '#3b82f6'
  }
}

// Custom hook for theme operations
export const useTheme = () => {
  const { theme, setTheme, toggleTheme, initTheme } = useThemeStore()
  
  // Initialize theme on mount
  React.useEffect(() => {
    initTheme()
  }, [initTheme])
  
  // Sync with system preference
  React.useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    
    const handleChange = (e) => {
      if (!localStorage.getItem('theme-storage')) {
        // Only apply system preference if user hasn't set a preference
        setTheme(e.matches ? 'dark' : 'light')
      }
    }
    
    mediaQuery.addEventListener('change', handleChange)
    
    return () => {
      mediaQuery.removeEventListener('change', handleChange)
    }
  }, [setTheme])
  
  return {
    theme,
    setTheme,
    toggleTheme,
    isDark: theme === 'dark',
    isLight: theme === 'light',
  }
}

// Hook to get theme-aware colors
export const useThemeColors = () => {
  const { isDark } = useTheme()
  
  return React.useMemo(() => ({
    // Background colors
    background: isDark ? '#111827' : '#f9fafb',
    surface: isDark ? '#1f2937' : '#ffffff',
    elevated: isDark ? '#374151' : '#ffffff',
    
    // Text colors
    text: {
      primary: isDark ? '#f3f4f6' : '#111827',
      secondary: isDark ? '#9ca3af' : '#6b7280',
      muted: isDark ? '#6b7280' : '#9ca3af',
    },
    
    // Border colors
    border: {
      default: isDark ? '#374151' : '#e5e7eb',
      light: isDark ? '#1f2937' : '#f3f4f6',
      dark: isDark ? '#4b5563' : '#d1d5db',
    },
    
    // Status colors
    status: {
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6',
    },
    
    // Chart colors
    chart: {
      primary: '#3b82f6',
      secondary: '#8b5cf6',
      tertiary: '#10b981',
      quaternary: '#f59e0b',
      quinary: '#ef4444',
    },
  }), [isDark])
}

// Hook for theme-aware styles
export const useThemeStyles = () => {
  const { isDark } = useTheme()
  
  return React.useMemo(() => ({
    // Glass morphism styles
    glass: isDark ? {
      background: 'rgba(31, 41, 55, 0.25)',
      backdropFilter: 'blur(10px)',
      WebkitBackdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.08)',
      boxShadow: '0 8px 32px 0 rgba(0, 0, 0, 0.3)',
    } : {
      background: 'rgba(255, 255, 255, 0.25)',
      backdropFilter: 'blur(10px)',
      WebkitBackdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.18)',
      boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.15)',
    },
    
    // Card styles
    card: isDark ? {
      background: '#1f2937',
      border: '1px solid #374151',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.3)',
    } : {
      background: '#ffffff',
      border: '1px solid #e5e7eb',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
    },
    
    // Input styles
    input: isDark ? {
      background: '#374151',
      border: '1px solid #4b5563',
      color: '#f3f4f6',
      placeholderColor: '#9ca3af',
    } : {
      background: '#ffffff',
      border: '1px solid #d1d5db',
      color: '#111827',
      placeholderColor: '#6b7280',
    },
  }), [isDark])
}

// Hook for theme transition
export const useThemeTransition = () => {
  const [isTransitioning, setIsTransitioning] = React.useState(false)
  const { theme, setTheme: setStoreTheme } = useThemeStore()
  
  const setThemeWithTransition = React.useCallback((newTheme) => {
    setIsTransitioning(true)
    
    // Add transition class
    document.documentElement.classList.add('theme-transition')
    
    // Set theme
    setStoreTheme(newTheme)
    
    // Remove transition class after animation
    setTimeout(() => {
      document.documentElement.classList.remove('theme-transition')
      setIsTransitioning(false)
    }, 300)
  }, [setStoreTheme])
  
  return {
    theme,
    setTheme: setThemeWithTransition,
    isTransitioning,
  }
}