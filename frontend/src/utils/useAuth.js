import React from 'react'
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { useMutation, useQueryClient } from 'react-query'
import { useNavigate } from 'react-router-dom'
import toast from 'react-hot-toast'

import { login, logout } from '../api/auth'
import { queryKeys } from '../api/client'

// Authentication store using Zustand
export const useAuthStore = create(
  persist(
    (set, get) => ({
      // State
      user: null,
      isAuthenticated: false,
      isLoading: false,
      
      // Actions
      setUser: (user) => set({ user, isAuthenticated: !!user }),
      setAuthenticated: (authenticated) => set({ isAuthenticated: authenticated }),
      setLoading: (loading) => set({ isLoading: loading }),
      
      // Clear all auth data
      clearAuth: () => set({ 
        user: null, 
        isAuthenticated: false, 
        isLoading: false 
      }),
      
      // Update user data
      updateUser: (updates) => set((state) => ({
        user: state.user ? { ...state.user, ...updates } : null
      })),
    }),
    {
      name: 'auth-storage',
      // Only persist user and isAuthenticated
      partialize: (state) => ({ 
        user: state.user,
        isAuthenticated: state.isAuthenticated 
      }),
    }
  )
)

// Custom hook for authentication operations
export const useAuth = () => {
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { 
    user, 
    isAuthenticated, 
    setUser, 
    setAuthenticated, 
    clearAuth,
    updateUser 
  } = useAuthStore()

  // Login mutation
  const loginMutation = useMutation(
    ({ username, password }) => login({ username, password }),
    {
      onMutate: () => {
        // Show loading state
        useAuthStore.getState().setLoading(true)
      },
      onSuccess: (data) => {
        if (data.user) {
          // Set user data
          setUser(data.user)
          setAuthenticated(true)
          
          // Invalidate queries to refresh data
          queryClient.invalidateQueries()
          
          // Show success message
          toast.success('Welcome back!')
          
          // Navigate to dashboard
          navigate('/', { replace: true })
        }
      },
      onError: (error) => {
        // Clear any existing auth
        clearAuth()
        
        // Error is already handled by API client
        console.error('Login error:', error)
      },
      onSettled: () => {
        // Clear loading state
        useAuthStore.getState().setLoading(false)
      },
    }
  )

  // Logout mutation
  const logoutMutation = useMutation(logout, {
    onMutate: () => {
      // Optimistically clear auth
      clearAuth()
    },
    onSuccess: () => {
      // Clear all queries
      queryClient.clear()
      
      // Show message
      toast.success('Logged out successfully')
      
      // Navigate to login
      navigate('/login', { replace: true })
    },
    onError: (error) => {
      console.error('Logout error:', error)
      // Even on error, ensure user is logged out locally
      clearAuth()
      queryClient.clear()
      navigate('/login', { replace: true })
    },
  })

  return {
    // State
    user,
    isAuthenticated,
    isLoading: useAuthStore((state) => state.isLoading),
    
    // Actions
    login: loginMutation.mutate,
    logout: logoutMutation.mutate,
    updateUser,
    
    // Mutation states
    isLoggingIn: loginMutation.isLoading,
    isLoggingOut: logoutMutation.isLoading,
    loginError: loginMutation.error,
  }
}

// Hook to require authentication
export const useRequireAuth = () => {
  const navigate = useNavigate()
  const { isAuthenticated, user } = useAuthStore()

  React.useEffect(() => {
    if (!isAuthenticated || !user) {
      navigate('/login', { replace: true })
    }
  }, [isAuthenticated, user, navigate])

  return { isAuthenticated, user }
}

// Hook to get current user
export const useCurrentUser = () => {
  const user = useAuthStore((state) => state.user)
  return user
}

// Hook to check if user has specific permission
export const useHasPermission = (permission) => {
  const user = useAuthStore((state) => state.user)
  
  // For this single-user app, all permissions are granted
  // This hook is here for potential future expansion
  return !!user
}

// Hook to handle authentication errors
export const useAuthError = () => {
  const clearAuth = useAuthStore((state) => state.clearAuth)
  const navigate = useNavigate()

  const handleAuthError = React.useCallback((error) => {
    if (error?.response?.status === 401) {
      clearAuth()
      navigate('/login', { replace: true })
      toast.error('Session expired. Please login again.')
    }
  }, [clearAuth, navigate])

  return handleAuthError
}