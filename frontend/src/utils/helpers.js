import { format, formatDistanceToNow, parseISO, isValid } from 'date-fns'
import { zonedTimeToUtc, utcToZonedTime } from 'date-fns-tz'

// Timezone configuration
export const CHICAGO_TIMEZONE = 'America/Chicago'

// Date and time utilities
export const dateHelpers = {
  // Convert UTC date to Chicago timezone
  toChicagoTime: (date) => {
    if (!date) return null
    
    const dateObj = typeof date === 'string' ? parseISO(date) : date
    if (!isValid(dateObj)) return null
    
    return utcToZonedTime(dateObj, CHICAGO_TIMEZONE)
  },

  // Convert Chicago time to UTC
  fromChicagoTime: (date) => {
    if (!date) return null
    
    const dateObj = typeof date === 'string' ? parseISO(date) : date
    if (!isValid(dateObj)) return null
    
    return zonedTimeToUtc(dateObj, CHICAGO_TIMEZONE)
  },

  // Format date for display
  formatDate: (date, formatString = 'MMM d, yyyy') => {
    if (!date) return ''
    
    const dateObj = typeof date === 'string' ? parseISO(date) : date
    if (!isValid(dateObj)) return ''
    
    const chicagoDate = utcToZonedTime(dateObj, CHICAGO_TIMEZONE)
    return format(chicagoDate, formatString)
  },

  // Format date and time for display
  formatDateTime: (date, formatString = 'MMM d, yyyy h:mm a') => {
    if (!date) return ''
    
    const dateObj = typeof date === 'string' ? parseISO(date) : date
    if (!isValid(dateObj)) return ''
    
    const chicagoDate = utcToZonedTime(dateObj, CHICAGO_TIMEZONE)
    return format(chicagoDate, formatString)
  },

  // Format relative time
  formatRelative: (date) => {
    if (!date) return ''
    
    const dateObj = typeof date === 'string' ? parseISO(date) : date
    if (!isValid(dateObj)) return ''
    
    return formatDistanceToNow(dateObj, { addSuffix: true })
  },
}

// Number formatting utilities
export const numberHelpers = {
  // Format currency
  formatCurrency: (amount, currency = 'USD') => {
    if (amount === null || amount === undefined) return '$0.00'
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount)
  },

  // Format percentage
  formatPercentage: (value, decimals = 1) => {
    if (value === null || value === undefined) return '0%'
    
    return `${value.toFixed(decimals)}%`
  },

  // Format number with commas
  formatNumber: (num) => {
    if (num === null || num === undefined) return '0'
    
    return new Intl.NumberFormat('en-US').format(num)
  },
}

// String utilities
export const stringHelpers = {
  // Truncate string
  truncate: (str, length = 50, suffix = '...') => {
    if (!str) return ''
    if (str.length <= length) return str
    
    return str.substring(0, length - suffix.length) + suffix
  },

  // Capitalize first letter
  capitalize: (str) => {
    if (!str) return ''
    return str.charAt(0).toUpperCase() + str.slice(1)
  },

  // Title case
  titleCase: (str) => {
    if (!str) return ''
    
    return str
      .toLowerCase()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  },

  // Generate initials
  getInitials: (name) => {
    if (!name) return ''
    
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2)
  },

  // Slugify
  slugify: (str) => {
    if (!str) return ''
    
    return str
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '')
  },
}

// Array utilities
export const arrayHelpers = {
  // Group by key
  groupBy: (array, key) => {
    return array.reduce((result, item) => {
      const group = item[key]
      if (!result[group]) result[group] = []
      result[group].push(item)
      return result
    }, {})
  },

  // Sort by key
  sortBy: (array, key, order = 'asc') => {
    return [...array].sort((a, b) => {
      if (order === 'asc') {
        return a[key] > b[key] ? 1 : -1
      } else {
        return a[key] < b[key] ? 1 : -1
      }
    })
  },

  // Remove duplicates
  unique: (array, key) => {
    if (key) {
      const seen = new Set()
      return array.filter(item => {
        const k = item[key]
        return seen.has(k) ? false : seen.add(k)
      })
    }
    return [...new Set(array)]
  },

  // Chunk array
  chunk: (array, size) => {
    const chunks = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  },
}

// Color utilities
export const colorHelpers = {
  // Hex to RGB
  hexToRgb: (hex) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null
  },

  // RGB to Hex
  rgbToHex: (r, g, b) => {
    return '#' + [r, g, b].map(x => {
      const hex = x.toString(16)
      return hex.length === 1 ? '0' + hex : hex
    }).join('')
  },

  // Get contrast color (black or white)
  getContrastColor: (hexColor) => {
    const rgb = colorHelpers.hexToRgb(hexColor)
    if (!rgb) return '#000000'
    
    const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255
    return luminance > 0.5 ? '#000000' : '#FFFFFF'
  },

  // Generate random color
  randomColor: () => {
    return '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')
  },
}

// Validation utilities
export const validationHelpers = {
  // Email validation
  isValidEmail: (email) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return re.test(email)
  },

  // Phone validation
  isValidPhone: (phone) => {
    const re = /^[\d\s\-\+\(\)]+$/
    return re.test(phone) && phone.replace(/\D/g, '').length >= 10
  },

  // URL validation
  isValidUrl: (url) => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  },
}

// Browser utilities
export const browserHelpers = {
  // Copy to clipboard
  copyToClipboard: async (text) => {
    try {
      await navigator.clipboard.writeText(text)
      return true
    } catch {
      // Fallback for older browsers
      const textarea = document.createElement('textarea')
      textarea.value = text
      textarea.style.position = 'fixed'
      textarea.style.opacity = '0'
      document.body.appendChild(textarea)
      textarea.select()
      
      try {
        document.execCommand('copy')
        document.body.removeChild(textarea)
        return true
      } catch {
        document.body.removeChild(textarea)
        return false
      }
    }
  },

  // Download file
  downloadFile: (content, filename, type = 'text/plain') => {
    const blob = new Blob([content], { type })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  },

  // Get browser info
  getBrowserInfo: () => {
    const ua = navigator.userAgent
    let browser = 'Unknown'
    
    if (ua.includes('Firefox')) browser = 'Firefox'
    else if (ua.includes('Chrome')) browser = 'Chrome'
    else if (ua.includes('Safari')) browser = 'Safari'
    else if (ua.includes('Edge')) browser = 'Edge'
    
    return {
      browser,
      isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(ua),
      isTouch: 'ontouchstart' in window,
    }
  },
}

// Local storage utilities
export const storageHelpers = {
  // Get item with fallback
  get: (key, fallback = null) => {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : fallback
    } catch {
      return fallback
    }
  },

  // Set item
  set: (key, value) => {
    try {
      localStorage.setItem(key, JSON.stringify(value))
      return true
    } catch {
      return false
    }
  },

  // Remove item
  remove: (key) => {
    try {
      localStorage.removeItem(key)
      return true
    } catch {
      return false
    }
  },

  // Clear all
  clear: () => {
    try {
      localStorage.clear()
      return true
    } catch {
      return false
    }
  },
}

// Debounce utility
export const debounce = (func, wait) => {
  let timeout
  
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// Throttle utility
export const throttle = (func, limit) => {
  let inThrottle
  
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// Deep clone utility
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (obj instanceof Object) {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

// Default export
export default {
  date: dateHelpers,
  number: numberHelpers,
  string: stringHelpers,
  array: arrayHelpers,
  color: colorHelpers,
  validation: validationHelpers,
  browser: browserHelpers,
  storage: storageHelpers,
  debounce,
  throttle,
  deepClone,
}