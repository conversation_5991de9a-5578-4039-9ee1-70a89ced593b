// Application constants
export const APP_NAME = 'Personal Organizer'
export const APP_VERSION = '1.0.0'
export const APP_DESCRIPTION = 'A world-class personal life management system'

// API configuration
export const API_BASE_URL = import.meta.env.VITE_API_URL || '/api'
export const API_TIMEOUT = 30000 // 30 seconds

// Authentication
export const AUTH_TOKEN_KEY = 'auth-token'
export const AUTH_REFRESH_INTERVAL = 1000 * 60 * 30 // 30 minutes

// Theme
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
}

export const DEFAULT_THEME = THEMES.LIGHT

// Dashboard
export const DASHBOARD_BREAKPOINTS = {
  lg: 1200,
  md: 996,
  sm: 768,
  xs: 480,
  xxs: 0,
}

export const DASHBOARD_COLS = {
  lg: 12,
  md: 10,
  sm: 6,
  xs: 4,
  xxs: 2,
}

export const DASHBOARD_ROW_HEIGHT = 60
export const DASHBOARD_MARGIN = [16, 16]

// Widget IDs
export const WIDGET_IDS = {
  WEATHER_CURRENT: 'weather-current',
  WEATHER_FORECAST: 'weather-forecast',
  BILLS_UPCOMING: 'bills-upcoming',
  EVENTS_UPCOMING: 'events-upcoming',
  BILLS_CHART: 'bills-chart',
  TODOS: 'todos',
  CONTACTS: 'contacts',
}

// Bills
export const BILL_CATEGORIES = {
  MORTGAGE: 'mortgage',
  UTILITIES: 'utilities',
  INSURANCE: 'insurance',
  CAR: 'car',
  VACATION: 'vacation',
  OTHER: 'other',
}

export const DEFAULT_BILL_CATEGORIES = [
  { id: BILL_CATEGORIES.MORTGAGE, name: 'Mortgage', color: '#3B82F6' },
  { id: BILL_CATEGORIES.UTILITIES, name: 'Utilities', color: '#10B981' },
  { id: BILL_CATEGORIES.INSURANCE, name: 'Insurance', color: '#F59E0B' },
  { id: BILL_CATEGORIES.CAR, name: 'Car Payment', color: '#EF4444' },
  { id: BILL_CATEGORIES.VACATION, name: 'Vacation', color: '#8B5CF6' },
  { id: BILL_CATEGORIES.OTHER, name: 'Other', color: '#6B7280' },
]

// Events
export const EVENT_COLORS = {
  ACTIVE: 'primary',
  PAST: 'gray',
  FUTURE: 'success',
}

// Contacts
export const CONTACT_STATUS = {
  GREEN: 'green',
  YELLOW: 'yellow',
  RED: 'red',
}

export const CONTACT_THRESHOLDS = {
  FAMILY: 7,
  CLOSE_FRIENDS: 14,
  FRIENDS: 21,
  BUSINESS: 30,
}

export const DEFAULT_CONTACT_TYPES = [
  { label: 'Family', threshold_days: CONTACT_THRESHOLDS.FAMILY },
  { label: 'Close Friends', threshold_days: CONTACT_THRESHOLDS.CLOSE_FRIENDS },
  { label: 'Friends', threshold_days: CONTACT_THRESHOLDS.FRIENDS },
  { label: 'Business', threshold_days: CONTACT_THRESHOLDS.BUSINESS },
]

// Todos
export const TODO_LIMITS = {
  INITIAL_LOAD: 50,
  LOAD_MORE: 50,
  VIRTUALIZATION_THRESHOLD: 300,
  MAX_DISPLAY: 1000,
}

// Weather
export const WEATHER_CITIES = {
  STL: {
    code: 'stl',
    name: 'St. Louis',
    state: 'MO',
    country: 'USA',
    timezone: 'America/Chicago',
  },
  ACCRA: {
    code: 'accra',
    name: 'Accra',
    state: null,
    country: 'Ghana',
    timezone: 'Africa/Accra',
  },
}

export const WEATHER_REFRESH_INTERVAL = 1000 * 60 * 60 * 2 // 2 hours
export const WEATHER_CACHE_KEY = 'weather-cache'

// Date formats
export const DATE_FORMATS = {
  SHORT: 'MMM d',
  MEDIUM: 'MMM d, yyyy',
  LONG: 'MMMM d, yyyy',
  TIME: 'h:mm a',
  DATETIME_SHORT: 'MMM d, h:mm a',
  DATETIME_MEDIUM: 'MMM d, yyyy h:mm a',
  DATETIME_LONG: 'MMMM d, yyyy h:mm a',
}

// Time zones
export const TIMEZONES = {
  CHICAGO: 'America/Chicago',
  UTC: 'UTC',
}

// Validation
export const VALIDATION = {
  USERNAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 20,
  },
  PASSWORD: {
    MIN_LENGTH: 8,
  },
  TODO: {
    MAX_LENGTH: 500,
  },
  BILL_TITLE: {
    MAX_LENGTH: 100,
  },
  EVENT_TITLE: {
    MAX_LENGTH: 100,
  },
  CONTACT_NAME: {
    MAX_LENGTH: 100,
  },
  NOTES: {
    MAX_LENGTH: 1000,
  },
}

// Rate limits
export const RATE_LIMITS = {
  LOGIN_ATTEMPTS: 5,
  LOGIN_WINDOW: 60, // 1 minute
  API_REQUESTS_PER_DAY: 200,
  API_REQUESTS_PER_HOUR: 50,
}

// Animation durations (ms)
export const ANIMATION_DURATION = {
  INSTANT: 0,
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  VERY_SLOW: 1000,
}

// Breakpoints (should match Tailwind)
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536,
}

// z-index levels
export const Z_INDEX = {
  DROPDOWN: 1000,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070,
  TOAST: 1080,
  OVERLAY: 1090,
}

// Local storage keys
export const STORAGE_KEYS = {
  AUTH: 'auth-storage',
  THEME: 'theme-storage',
  DASHBOARD_LAYOUT: 'dashboard-layout',
  USER_PREFERENCES: 'user-preferences',
}

// Error messages
export const ERROR_MESSAGES = {
  GENERIC: 'An error occurred. Please try again.',
  NETWORK: 'Network error. Please check your connection.',
  AUTH_REQUIRED: 'Authentication required',
  SESSION_EXPIRED: 'Session expired. Please login again.',
  INVALID_CREDENTIALS: 'Invalid username or password',
  RATE_LIMIT: 'Too many requests. Please try again later.',
  NOT_FOUND: 'Resource not found',
  SERVER_ERROR: 'Server error. Please try again later.',
  VALIDATION_FAILED: 'Please check your input and try again.',
}

// Success messages
export const SUCCESS_MESSAGES = {
  LOGIN: 'Welcome back!',
  LOGOUT: 'Logged out successfully',
  PROFILE_UPDATED: 'Profile updated successfully',
  PASSWORD_UPDATED: 'Password updated successfully',
  BILL_CREATED: 'Bill created successfully',
  BILL_UPDATED: 'Bill updated successfully',
  BILL_DELETED: 'Bill deleted successfully',
  EVENT_CREATED: 'Event created successfully',
  EVENT_UPDATED: 'Event updated successfully',
  EVENT_DELETED: 'Event deleted successfully',
  CONTACT_CREATED: 'Contact created successfully',
  CONTACT_UPDATED: 'Contact updated successfully',
  CONTACT_DELETED: 'Contact deleted successfully',
  CONTACT_PINGED: 'Contact updated successfully',
  TODO_CREATED: 'Todo added',
  TODO_COMPLETED: 'Todo completed!',
  LAYOUT_SAVED: 'Layout saved successfully',
  DASHBOARD_REFRESHED: 'Dashboard refreshed',
}

// Keyboard shortcuts
export const KEYBOARD_SHORTCUTS = {
  SAVE: 'cmd+s, ctrl+s',
  NEW_TODO: 'cmd+n, ctrl+n',
  SEARCH: 'cmd+k, ctrl+k',
  TOGGLE_THEME: 'cmd+shift+d, ctrl+shift+d',
  ESCAPE: 'esc',
}

// Feature flags
export const FEATURES = {
  MULTI_USER: false,
  TWO_FACTOR_AUTH: false,
  EMAIL_NOTIFICATIONS: false,
  MOBILE_APP: false,
  OFFLINE_MODE: false,
  ADVANCED_ANALYTICS: false,
}

// Export all constants as default
export default {
  APP_NAME,
  APP_VERSION,
  APP_DESCRIPTION,
  API_BASE_URL,
  API_TIMEOUT,
  AUTH_TOKEN_KEY,
  AUTH_REFRESH_INTERVAL,
  THEMES,
  DEFAULT_THEME,
  DASHBOARD_BREAKPOINTS,
  DASHBOARD_COLS,
  DASHBOARD_ROW_HEIGHT,
  DASHBOARD_MARGIN,
  WIDGET_IDS,
  BILL_CATEGORIES,
  DEFAULT_BILL_CATEGORIES,
  EVENT_COLORS,
  CONTACT_STATUS,
  CONTACT_THRESHOLDS,
  DEFAULT_CONTACT_TYPES,
  TODO_LIMITS,
  WEATHER_CITIES,
  WEATHER_REFRESH_INTERVAL,
  WEATHER_CACHE_KEY,
  DATE_FORMATS,
  TIMEZONES,
  VALIDATION,
  RATE_LIMITS,
  ANIMATION_DURATION,
  BREAKPOINTS,
  Z_INDEX,
  STORAGE_KEYS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  KEYBOARD_SHORTCUTS,
  FEATURES,
}