import React from 'react'
import { motion } from 'framer-motion'
import { 
  Cloud, 
  CloudDrizzle, 
  CloudFog, 
  CloudLightning, 
  CloudRain, 
  CloudSnow, 
  Sun, 
  HelpCircle,
  MapPin,
  Thermometer,
  Calendar
} from 'lucide-react'
import { formatWeatherForDisplay, formatForecastDay, WEATHER_CITIES } from '../../api/weather'

// Weather icon mapping
const WEATHER_ICONS = {
  'sun': Sun,
  'cloud-sun': Cloud,
  'cloud': Cloud,
  'cloud-fog': CloudFog,
  'cloud-drizzle': CloudDrizzle,
  'cloud-rain': CloudRain,
  'cloud-snow': CloudSnow,
  'cloud-lightning': CloudLightning,
  'help-circle': HelpCircle,
}

const WeatherWidget = ({ widgetId, data, isEditMode }) => {
  // Determine if this is current weather or forecast widget
  const isCurrentWeather = widgetId === 'weather-current'
  
  // Get weather data for both cities
  const stlWeather = data?.weather?.stl ? formatWeatherForDisplay(data.weather.stl) : null
  const accraWeather = data?.weather?.accra ? formatWeatherForDisplay(data.weather.accra) : null

  if (!stlWeather && !accraWeather) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-gray-500 dark:text-gray-400">
          No weather data available
        </p>
      </div>
    )
  }

  if (isCurrentWeather) {
    return <CurrentWeatherDisplay stlWeather={stlWeather} accraWeather={accraWeather} />
  } else {
    return <ForecastDisplay stlWeather={stlWeather} accraWeather={accraWeather} />
  }
}

// Current weather display component
const CurrentWeatherDisplay = ({ stlWeather, accraWeather }) => {
  return (
    <div className="space-y-4">
      {/* St. Louis Weather */}
      {stlWeather && (
        <WeatherCard
          city={WEATHER_CITIES.stl}
          weather={stlWeather.current}
          isLarge={true}
        />
      )}
      
      {/* Accra Weather */}
      {accraWeather && (
        <WeatherCard
          city={WEATHER_CITIES.accra}
          weather={accraWeather.current}
          isLarge={true}
        />
      )}
    </div>
  )
}

// Forecast display component
const ForecastDisplay = ({ stlWeather, accraWeather }) => {
  const [selectedCity, setSelectedCity] = React.useState('stl')
  
  const weatherData = selectedCity === 'stl' ? stlWeather : accraWeather
  const cityInfo = WEATHER_CITIES[selectedCity]

  if (!weatherData?.forecast || weatherData.forecast.length === 0) {
    return (
      <div className="text-center text-gray-500 dark:text-gray-400">
        No forecast data available
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* City selector */}
      <div className="flex justify-center gap-2">
        {Object.entries(WEATHER_CITIES).map(([key, city]) => {
          const hasData = key === 'stl' ? !!stlWeather : !!accraWeather
          if (!hasData) return null
          
          return (
            <button
              key={key}
              onClick={() => setSelectedCity(key)}
              className={`px-3 py-1 rounded-lg text-sm font-medium transition-all ${
                selectedCity === key
                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800'
              }`}
            >
              {city.name}
            </button>
          )
        })}
      </div>

      {/* Forecast cards */}
      <div className="grid grid-cols-3 gap-3">
        {weatherData.forecast.map((day, index) => (
          <ForecastCard key={index} day={day} index={index} />
        ))}
      </div>
    </div>
  )
}

// Weather card component for current weather
const WeatherCard = ({ city, weather, isLarge }) => {
  const IconComponent = WEATHER_ICONS[weather.icon] || WEATHER_ICONS['help-circle']
  
  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      className={`
        relative overflow-hidden rounded-xl p-4
        bg-gradient-to-br from-blue-400 to-blue-600 dark:from-blue-600 dark:to-blue-800
        text-white shadow-lg
        ${isLarge ? 'min-h-[120px]' : 'min-h-[80px]'}
      `}
    >
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute -top-24 -right-24 w-48 h-48 bg-white rounded-full" />
        <div className="absolute -bottom-24 -left-24 w-48 h-48 bg-white rounded-full" />
      </div>

      {/* Content */}
      <div className="relative z-10">
        {/* City name */}
        <div className="flex items-center gap-1 text-sm opacity-90 mb-2">
          <MapPin className="w-3 h-3" />
          <span>{city.name}</span>
        </div>

        {/* Temperature and condition */}
        <div className="flex items-center justify-between">
          <div>
            <div className={`flex items-center ${isLarge ? 'text-4xl' : 'text-2xl'} font-bold`}>
              <Thermometer className="w-6 h-6 mr-1 opacity-80" />
              {weather.temperatureDisplay}
            </div>
            <p className="text-sm opacity-90 mt-1">{weather.condition}</p>
          </div>
          
          <IconComponent className={`${isLarge ? 'w-16 h-16' : 'w-12 h-12'} opacity-80`} />
        </div>
      </div>
    </motion.div>
  )
}

// Forecast card component
const ForecastCard = ({ day, index }) => {
  const IconComponent = WEATHER_ICONS[day.icon] || WEATHER_ICONS['help-circle']
  const dayLabel = formatForecastDay(day.date)
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 text-center"
    >
      {/* Day */}
      <div className="flex items-center justify-center gap-1 text-xs text-gray-600 dark:text-gray-400 mb-2">
        <Calendar className="w-3 h-3" />
        <span className="font-medium">{dayLabel}</span>
      </div>
      
      {/* Icon */}
      <div className="flex justify-center mb-2">
        <IconComponent className="w-8 h-8 text-gray-700 dark:text-gray-300" />
      </div>
      
      {/* Temperature range */}
      <div className="text-sm">
        <span className="font-semibold text-gray-900 dark:text-white">
          {day.highDisplay}
        </span>
        <span className="text-gray-500 dark:text-gray-400 mx-1">/</span>
        <span className="text-gray-600 dark:text-gray-400">
          {day.lowDisplay}
        </span>
      </div>
      
      {/* Condition */}
      <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 truncate">
        {day.condition}
      </p>
    </motion.div>
  )
}

export default WeatherWidget