import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Users, 
  User, 
  Phone, 
  MessageCircle, 
  Plus, 
  Edit2, 
  Trash2,
  AlertCircle,
  CheckCircle,
  Clock,
  Calendar,
  Heart,
  Briefcase,
  UserPlus,
  ChevronRight
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { useMutation, useQueryClient } from 'react-query'
import toast from 'react-hot-toast'

import { 
  deleteContact, 
  pingContact, 
  formatContactForDisplay, 
  getContactStatusColor,
  formatLastContactDisplay,
  sortContactsByUrgency 
} from '../../api/contacts'
import { queryKeys } from '../../api/client'

// Contact type icon mapping
const CONTACT_TYPE_ICONS = {
  'Family': Heart,
  'Close Friends': Users,
  'Friends': User,
  'Business': Briefcase,
}

const ContactsWidget = ({ widgetId, data, isEditMode }) => {
  const queryClient = useQueryClient()
  const [showAll, setShowAll] = useState(false)
  const [selectedContact, setSelectedContact] = useState(null)
  const [groupByType, setGroupByType] = useState(false)

  // Get contacts data
  const contacts = (data?.contacts || [])
    .map(contact => formatContactForDisplay(contact))
  
  // Sort contacts by urgency
  const sortedContacts = sortContactsByUrgency(contacts)
  
  // Get counts by status
  const statusCounts = {
    red: contacts.filter(c => c.status === 'red').length,
    yellow: contacts.filter(c => c.status === 'yellow').length,
    green: contacts.filter(c => c.status === 'green').length,
  }

  // Limit display if not showing all
  const displayedContacts = showAll ? sortedContacts : sortedContacts.slice(0, 8)

  // Ping contact mutation
  const pingContactMutation = useMutation(
    (contactId) => pingContact(contactId),
    {
      onSuccess: () => {
        toast.success('Contact updated successfully')
        queryClient.invalidateQueries(queryKeys.dashboard.all)
        queryClient.invalidateQueries(queryKeys.contacts.all)
      },
      onError: () => {
        toast.error('Failed to update contact')
      },
    }
  )

  // Delete contact mutation
  const deleteContactMutation = useMutation(
    (contactId) => deleteContact(contactId),
    {
      onSuccess: () => {
        toast.success('Contact deleted successfully')
        queryClient.invalidateQueries(queryKeys.dashboard.all)
        queryClient.invalidateQueries(queryKeys.contacts.all)
        setSelectedContact(null)
      },
      onError: () => {
        toast.error('Failed to delete contact')
      },
    }
  )

  const handlePing = (contactId) => {
    pingContactMutation.mutate(contactId)
  }

  const handleDelete = (contactId) => {
    if (window.confirm('Are you sure you want to delete this contact?')) {
      deleteContactMutation.mutate(contactId)
    }
  }

  if (contacts.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-4">
        <Users className="w-12 h-12 text-gray-300 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No contacts yet</p>
        <button 
          className="mt-3 btn-primary btn-sm"
          onClick={() => toast.info('Add Contact feature coming soon!')}
        >
          <UserPlus className="w-4 h-4 mr-1" />
          Add Contact
        </button>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Status summary */}
      <div className="grid grid-cols-3 gap-2 mb-4">
        <StatusCard status="red" count={statusCounts.red} label="Overdue" />
        <StatusCard status="yellow" count={statusCounts.yellow} label="Soon" />
        <StatusCard status="green" count={statusCounts.green} label="Good" />
      </div>

      {/* View toggle */}
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Contacts to reach out to
        </h3>
        <button
          onClick={() => setGroupByType(!groupByType)}
          className="text-xs text-primary-600 dark:text-primary-400 hover:underline"
        >
          {groupByType ? 'Sort by urgency' : 'Group by type'}
        </button>
      </div>

      {/* Contacts list */}
      <div className="flex-1 space-y-2 overflow-auto">
        <AnimatePresence mode="popLayout">
          {displayedContacts.map((contact, index) => (
            <ContactItem
              key={contact.id}
              contact={contact}
              index={index}
              onSelect={() => setSelectedContact(contact)}
              onPing={() => handlePing(contact.id)}
              onDelete={() => handleDelete(contact.id)}
              isSelected={selectedContact?.id === contact.id}
              isPinging={pingContactMutation.isLoading}
            />
          ))}
        </AnimatePresence>
      </div>

      {/* Show more/less button */}
      {contacts.length > 8 && (
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => setShowAll(!showAll)}
          className="mt-3 w-full py-2 text-sm text-primary-600 dark:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-lg transition-colors"
        >
          {showAll ? 'Show Less' : `Show ${contacts.length - 8} More`}
        </motion.button>
      )}

      {/* Add contact button */}
      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={() => toast.info('Add Contact feature coming soon!')}
        className="mt-3 w-full btn-primary py-2"
      >
        <UserPlus className="w-4 h-4 mr-2" />
        Add Contact
      </motion.button>
    </div>
  )
}

// Status card component
const StatusCard = ({ status, count, label }) => {
  const colors = {
    red: 'bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800',
    yellow: 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800',
    green: 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800',
  }

  return (
    <div className={`p-2 rounded-lg border text-center ${colors[status]}`}>
      <div className="text-2xl font-bold">{count}</div>
      <div className="text-xs opacity-80">{label}</div>
    </div>
  )
}

// Individual contact item component
const ContactItem = ({ contact, index, onSelect, onPing, onDelete, isSelected, isPinging }) => {
  const [showActions, setShowActions] = useState(false)
  
  const statusColors = getContactStatusColor(contact)
  const lastContactDisplay = formatLastContactDisplay(contact.lastContact, contact.daysSinceContact)
  const TypeIcon = CONTACT_TYPE_ICONS[contact.typeName] || User

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      transition={{ delay: index * 0.05 }}
      whileHover={{ scale: 1.01 }}
      className={`
        relative p-3 rounded-lg border transition-all cursor-pointer
        ${isSelected 
          ? 'border-primary-400 bg-primary-50 dark:bg-primary-900/20' 
          : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
        }
      `}
      onClick={onSelect}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div className="flex items-center gap-3">
        {/* Status indicator */}
        <div className={`w-2 h-2 rounded-full flex-shrink-0 ${
          contact.status === 'red' ? 'bg-red-500' :
          contact.status === 'yellow' ? 'bg-yellow-500' :
          'bg-green-500'
        }`} />

        {/* Contact type icon */}
        <div className="w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center flex-shrink-0">
          <TypeIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
        </div>

        {/* Contact details */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between gap-2">
            <h4 className="font-medium text-gray-900 dark:text-white truncate">
              {contact.name}
            </h4>
            
            {/* Ping button */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={(e) => {
                e.stopPropagation()
                onPing()
              }}
              disabled={isPinging}
              className={`
                px-3 py-1 text-xs font-medium rounded-full transition-all
                ${statusColors.bg} ${statusColors.text}
                hover:opacity-80 disabled:opacity-50
              `}
            >
              {isPinging ? (
                <span className="flex items-center gap-1">
                  <span className="w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  Updating...
                </span>
              ) : (
                <span className="flex items-center gap-1">
                  <MessageCircle className="w-3 h-3" />
                  Ping
                </span>
              )}
            </motion.button>
          </div>

          {/* Contact info */}
          <div className="flex items-center gap-3 mt-1">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {contact.typeName}
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
              <Clock className="w-3 h-3" />
              {lastContactDisplay}
            </span>
          </div>

          {/* Notes preview */}
          {contact.notes && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">
              {contact.notes}
            </p>
          )}
        </div>

        {/* Action buttons */}
        <AnimatePresence>
          {showActions && !isPinging && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="flex items-center gap-1"
            >
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  // Handle edit
                }}
                className="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
                aria-label="Edit contact"
              >
                <Edit2 className="w-3 h-3" />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onDelete()
                }}
                className="p-1.5 text-gray-400 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors"
                aria-label="Delete contact"
              >
                <Trash2 className="w-3 h-3" />
              </button>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  )
}

export default ContactsWidget