import React from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts'
import { motion } from 'framer-motion'
import { 
  DollarSign, 
  TrendingUp, 
  Pie<PERSON>hart as PieChartIcon,
  Home,
  Car,
  Shield,
  Zap,
  Plane,
  Tag
} from 'lucide-react'

// Category icon mapping
const CATEGORY_ICONS = {
  mortgage: Home,
  utilities: Zap,
  insurance: Shield,
  car: Car,
  vacation: Plane,
  other: Tag,
}

const ChartWidget = ({ widgetId, data, isEditMode }) => {
  // Get bills by category data
  const billsByCategory = data?.billsByCategory || {}
  const categories = data?.user?.categories || []
  
  // Transform data for the chart
  const chartData = Object.entries(billsByCategory)
    .filter(([categoryId, data]) => categoryId && data.total > 0)
    .map(([categoryId, data]) => {
      const category = categories.find(c => c.id === categoryId) || {
        name: categoryId,
        color: '#6B7280'
      }
      
      return {
        name: category.name,
        value: data.total,
        count: data.count,
        color: category.color,
        categoryId: categoryId,
      }
    })
    .sort((a, b) => b.value - a.value)

  // Calculate total
  const totalAmount = chartData.reduce((sum, item) => sum + item.value, 0)

  if (chartData.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-4">
        <PieChartIcon className="w-12 h-12 text-gray-300 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No bills data to display</p>
        <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
          Add bills with amounts to see the breakdown
        </p>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Total amount */}
      <div className="text-center mb-4">
        <p className="text-sm text-gray-600 dark:text-gray-400">Total Monthly</p>
        <p className="text-2xl font-bold text-gray-900 dark:text-white">
          ${totalAmount.toFixed(2)}
        </p>
      </div>

      {/* Pie chart */}
      <div className="flex-1 min-h-[200px]">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
              animationBegin={0}
              animationDuration={800}
            >
              {chartData.map((entry, index) => (
                <Cell 
                  key={`cell-${index}`} 
                  fill={entry.color}
                  stroke={entry.color}
                  strokeWidth={2}
                />
              ))}
            </Pie>
            <Tooltip 
              content={<CustomTooltip />}
              wrapperStyle={{ outline: 'none' }}
            />
          </PieChart>
        </ResponsiveContainer>
      </div>

      {/* Legend */}
      <div className="mt-4 space-y-2">
        {chartData.map((item, index) => {
          const Icon = CATEGORY_ICONS[item.categoryId] || CATEGORY_ICONS.other
          const percentage = ((item.value / totalAmount) * 100).toFixed(1)
          
          return (
            <motion.div
              key={item.categoryId}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <div className="flex items-center gap-3">
                {/* Color indicator */}
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: item.color }}
                />
                
                {/* Icon and name */}
                <div className="flex items-center gap-2">
                  <Icon className="w-4 h-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {item.name}
                  </span>
                </div>
              </div>

              {/* Amount and percentage */}
              <div className="text-right">
                <div className="text-sm font-semibold text-gray-900 dark:text-white">
                  ${item.value.toFixed(2)}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {percentage}% • {item.count} {item.count === 1 ? 'bill' : 'bills'}
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Summary stats */}
      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-2 gap-4 text-center">
          <div>
            <p className="text-xs text-gray-500 dark:text-gray-400">Categories</p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {chartData.length}
            </p>
          </div>
          <div>
            <p className="text-xs text-gray-500 dark:text-gray-400">Avg per Bill</p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              ${(totalAmount / chartData.reduce((sum, item) => sum + item.count, 0)).toFixed(2)}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

// Custom tooltip component
const CustomTooltip = ({ active, payload }) => {
  if (!active || !payload || !payload[0]) return null

  const data = payload[0].payload
  const Icon = CATEGORY_ICONS[data.categoryId] || CATEGORY_ICONS.other

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700"
    >
      <div className="flex items-center gap-2 mb-2">
        <div 
          className="w-3 h-3 rounded-full"
          style={{ backgroundColor: data.color }}
        />
        <Icon className="w-4 h-4 text-gray-500" />
        <span className="font-medium text-gray-900 dark:text-white">
          {data.name}
        </span>
      </div>
      <div className="space-y-1">
        <div className="flex justify-between gap-4">
          <span className="text-sm text-gray-600 dark:text-gray-400">Total:</span>
          <span className="text-sm font-semibold text-gray-900 dark:text-white">
            ${data.value.toFixed(2)}
          </span>
        </div>
        <div className="flex justify-between gap-4">
          <span className="text-sm text-gray-600 dark:text-gray-400">Bills:</span>
          <span className="text-sm font-semibold text-gray-900 dark:text-white">
            {data.count}
          </span>
        </div>
        <div className="flex justify-between gap-4">
          <span className="text-sm text-gray-600 dark:text-gray-400">Average:</span>
          <span className="text-sm font-semibold text-gray-900 dark:text-white">
            ${(data.value / data.count).toFixed(2)}
          </span>
        </div>
      </div>
    </motion.div>
  )
}

export default ChartWidget