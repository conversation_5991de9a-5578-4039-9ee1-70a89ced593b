import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Calendar, 
  Clock, 
  MapPin, 
  Plus, 
  Edit2, 
  Trash2,
  AlertCircle,
  CheckCircle,
  MoreVertical,
  CalendarDays,
  Timer,
  Repeat
} from 'lucide-react'
import { format, isAfter, isBefore, isWithinInterval, startOfDay, endOfDay } from 'date-fns'
import { useMutation, useQueryClient } from 'react-query'
import toast from 'react-hot-toast'

import { deleteEvent, formatEventForDisplay, isEventActive, isEventPast, formatEventDuration } from '../../api/events'
import { queryKeys } from '../../api/client'

const EventsWidget = ({ widgetId, data, isEditMode }) => {
  const queryClient = useQueryClient()
  const [showAll, setShowAll] = useState(false)
  const [selectedEvent, setSelectedEvent] = useState(null)

  // Get events data
  const events = data?.events || []
  
  // Format and sort events
  const upcomingEvents = events
    .map(event => formatEventForDisplay(event))
    .filter(event => !isEventPast(event))
    .sort((a, b) => a.startDate - b.startDate)

  // Limit display if not showing all
  const displayedEvents = showAll ? upcomingEvents : upcomingEvents.slice(0, 5)

  // Delete event mutation
  const deleteEventMutation = useMutation(
    (eventId) => deleteEvent(eventId),
    {
      onSuccess: () => {
        toast.success('Event deleted successfully')
        queryClient.invalidateQueries(queryKeys.dashboard.all)
        queryClient.invalidateQueries(queryKeys.events.all)
        setSelectedEvent(null)
      },
      onError: () => {
        toast.error('Failed to delete event')
      },
    }
  )

  const handleDelete = (eventId) => {
    if (window.confirm('Are you sure you want to delete this event?')) {
      deleteEventMutation.mutate(eventId)
    }
  }

  if (upcomingEvents.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-4">
        <CalendarDays className="w-12 h-12 text-gray-300 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No upcoming events</p>
        <button 
          className="mt-3 btn-primary btn-sm"
          onClick={() => toast.info('Add Event feature coming soon!')}
        >
          <Plus className="w-4 h-4 mr-1" />
          Add Event
        </button>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Events list */}
      <div className="flex-1 space-y-2 overflow-auto">
        <AnimatePresence mode="popLayout">
          {displayedEvents.map((event, index) => (
            <EventItem
              key={event.id}
              event={event}
              index={index}
              onSelect={() => setSelectedEvent(event)}
              onDelete={() => handleDelete(event.id)}
              isSelected={selectedEvent?.id === event.id}
            />
          ))}
        </AnimatePresence>
      </div>

      {/* Show more/less button */}
      {upcomingEvents.length > 5 && (
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => setShowAll(!showAll)}
          className="mt-3 w-full py-2 text-sm text-primary-600 dark:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-lg transition-colors"
        >
          {showAll ? 'Show Less' : `Show ${upcomingEvents.length - 5} More`}
        </motion.button>
      )}

      {/* Add event button */}
      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={() => toast.info('Add Event feature coming soon!')}
        className="mt-3 w-full btn-primary py-2"
      >
        <Plus className="w-4 h-4 mr-2" />
        Add Event
      </motion.button>
    </div>
  )
}

// Individual event item component
const EventItem = ({ event, index, onSelect, onDelete, isSelected }) => {
  const [showActions, setShowActions] = useState(false)
  
  // Check if event is happening now
  const isActive = isEventActive(event)
  const duration = formatEventDuration(event)
  
  // Format time display
  const timeDisplay = event.startDate 
    ? format(event.startDate, 'h:mm a')
    : 'No time'
    
  // Format date display
  const dateDisplay = event.startDate
    ? format(event.startDate, 'MMM d, yyyy')
    : 'No date'

  // Check if event is today
  const isToday = event.startDate && isWithinInterval(new Date(), {
    start: startOfDay(event.startDate),
    end: endOfDay(event.startDate)
  })

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      transition={{ delay: index * 0.05 }}
      whileHover={{ scale: 1.01 }}
      className={`
        relative p-3 rounded-lg border transition-all cursor-pointer
        ${isSelected 
          ? 'border-primary-400 bg-primary-50 dark:bg-primary-900/20' 
          : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
        }
        ${isActive ? 'bg-green-50 dark:bg-green-900/10 border-green-300 dark:border-green-700' : ''}
        ${isToday && !isActive ? 'bg-blue-50 dark:bg-blue-900/10' : ''}
      `}
      onClick={onSelect}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div className="flex items-start gap-3">
        {/* Time indicator */}
        <div className={`
          w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0
          ${isActive 
            ? 'bg-green-100 dark:bg-green-900/30' 
            : isToday
            ? 'bg-blue-100 dark:bg-blue-900/30'
            : 'bg-gray-100 dark:bg-gray-800'
          }
        `}>
          <Clock className={`
            w-5 h-5
            ${isActive 
              ? 'text-green-600 dark:text-green-400' 
              : isToday
              ? 'text-blue-600 dark:text-blue-400'
              : 'text-gray-600 dark:text-gray-400'
            }
          `} />
        </div>

        {/* Event details */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <h4 className="font-medium text-gray-900 dark:text-white truncate">
              {event.title}
              {event.isRecurring && (
                <Repeat className="inline-block w-3 h-3 ml-1 text-gray-400" />
              )}
            </h4>
            
            {/* Duration */}
            {duration && (
              <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
                <Timer className="w-3 h-3" />
                {duration}
              </span>
            )}
          </div>

          {/* Date and time */}
          <div className="flex items-center gap-3 mt-1">
            <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400">
              <Calendar className="w-3 h-3" />
              <span>{dateDisplay}</span>
            </div>
            <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400">
              <Clock className="w-3 h-3" />
              <span>{timeDisplay}</span>
            </div>
          </div>

          {/* Status indicators */}
          <div className="flex items-center gap-2 mt-1">
            {isActive && (
              <span className="flex items-center gap-1 text-xs text-green-600 dark:text-green-400">
                <CheckCircle className="w-3 h-3" />
                Happening now
              </span>
            )}
            {isToday && !isActive && (
              <span className="flex items-center gap-1 text-xs text-blue-600 dark:text-blue-400">
                <AlertCircle className="w-3 h-3" />
                Today
              </span>
            )}
          </div>

          {/* Notes preview */}
          {event.notes && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">
              {event.notes}
            </p>
          )}
        </div>

        {/* Action buttons */}
        <AnimatePresence>
          {showActions && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="flex items-center gap-1"
            >
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  // Handle edit
                }}
                className="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
                aria-label="Edit event"
              >
                <Edit2 className="w-4 h-4" />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onDelete()
                }}
                className="p-1.5 text-gray-400 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors"
                aria-label="Delete event"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  )
}

export default EventsWidget