import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  DollarSign, 
  Calendar, 
  Tag, 
  Plus, 
  Edit2, 
  Trash2, 
  CheckCircle,
  AlertCircle,
  Clock,
  Plane,
  Home,
  Car,
  Shield,
  Zap,
  MoreHorizontal
} from 'lucide-react'
import { format, isAfter, isBefore, startOfDay, addDays } from 'date-fns'
import { useMutation, useQueryClient } from 'react-query'
import toast from 'react-hot-toast'

import { deleteBill, formatBillForDisplay } from '../../api/bills'
import { queryKeys } from '../../api/client'

// Category icon mapping
const CATEGORY_ICONS = {
  mortgage: Home,
  utilities: Zap,
  insurance: Shield,
  car: Car,
  vacation: Plane,
  other: Tag,
}

// Get category color
const getCategoryColor = (category, categories) => {
  const cat = categories?.find(c => c.id === category)
  return cat?.color || '#6B7280'
}

const BillsWidget = ({ widgetId, data, isEditMode }) => {
  const queryClient = useQueryClient()
  const [showAll, setShowAll] = useState(false)
  const [selectedBill, setSelectedBill] = useState(null)

  // Get bills data
  const bills = data?.bills || []
  const categories = data?.user?.categories || []
  
  // Filter and sort bills
  const upcomingBills = bills
    .map(bill => formatBillForDisplay(bill))
    .sort((a, b) => {
      if (!a.dueDate) return 1
      if (!b.dueDate) return -1
      return a.dueDate - b.dueDate
    })

  // Limit display if not showing all
  const displayedBills = showAll ? upcomingBills : upcomingBills.slice(0, 5)

  // Delete bill mutation
  const deleteBillMutation = useMutation(
    (billId) => deleteBill(billId),
    {
      onSuccess: () => {
        toast.success('Bill deleted successfully')
        queryClient.invalidateQueries(queryKeys.dashboard.all)
        queryClient.invalidateQueries(queryKeys.bills.all)
        setSelectedBill(null)
      },
      onError: () => {
        toast.error('Failed to delete bill')
      },
    }
  )

  const handleDelete = (billId) => {
    if (window.confirm('Are you sure you want to delete this bill?')) {
      deleteBillMutation.mutate(billId)
    }
  }

  if (upcomingBills.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-4">
        <DollarSign className="w-12 h-12 text-gray-300 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No upcoming bills</p>
        <button 
          className="mt-3 btn-primary btn-sm"
          onClick={() => toast.info('Add Bill feature coming soon!')}
        >
          <Plus className="w-4 h-4 mr-1" />
          Add Bill
        </button>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Bills list */}
      <div className="flex-1 space-y-2 overflow-auto">
        <AnimatePresence mode="popLayout">
          {displayedBills.map((bill, index) => (
            <BillItem
              key={bill.id}
              bill={bill}
              categories={categories}
              index={index}
              onSelect={() => setSelectedBill(bill)}
              onDelete={() => handleDelete(bill.id)}
              isSelected={selectedBill?.id === bill.id}
            />
          ))}
        </AnimatePresence>
      </div>

      {/* Show more/less button */}
      {upcomingBills.length > 5 && (
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => setShowAll(!showAll)}
          className="mt-3 w-full py-2 text-sm text-primary-600 dark:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-lg transition-colors"
        >
          {showAll ? 'Show Less' : `Show ${upcomingBills.length - 5} More`}
        </motion.button>
      )}

      {/* Add bill button */}
      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={() => toast.info('Add Bill feature coming soon!')}
        className="mt-3 w-full btn-primary py-2"
      >
        <Plus className="w-4 h-4 mr-2" />
        Add Bill or Vacation
      </motion.button>
    </div>
  )
}

// Individual bill item component
const BillItem = ({ bill, categories, index, onSelect, onDelete, isSelected }) => {
  const [showActions, setShowActions] = useState(false)
  
  // Determine bill status
  const today = startOfDay(new Date())
  const dueDate = bill.dueDate ? startOfDay(bill.dueDate) : null
  const isOverdue = dueDate && isBefore(dueDate, today)
  const isDueSoon = dueDate && !isOverdue && isBefore(dueDate, addDays(today, 7))
  
  // Get category info
  const categoryColor = getCategoryColor(bill.category, categories)
  const CategoryIcon = CATEGORY_ICONS[bill.category] || CATEGORY_ICONS.other
  
  // Determine if this is a vacation
  const isVacation = bill.category === 'vacation' || 
    bill.title.toLowerCase().includes('vacation') || 
    bill.title.toLowerCase().includes('trip')

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      transition={{ delay: index * 0.05 }}
      whileHover={{ scale: 1.01 }}
      className={`
        relative p-3 rounded-lg border transition-all cursor-pointer
        ${isSelected 
          ? 'border-primary-400 bg-primary-50 dark:bg-primary-900/20' 
          : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
        }
        ${isOverdue ? 'bg-red-50 dark:bg-red-900/10' : ''}
        ${isDueSoon && !isOverdue ? 'bg-yellow-50 dark:bg-yellow-900/10' : ''}
      `}
      onClick={onSelect}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div className="flex items-start gap-3">
        {/* Category icon */}
        <div 
          className="w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0"
          style={{ backgroundColor: `${categoryColor}20` }}
        >
          <CategoryIcon 
            className="w-5 h-5" 
            style={{ color: categoryColor }}
          />
        </div>

        {/* Bill details */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <h4 className="font-medium text-gray-900 dark:text-white truncate">
              {bill.title}
              {isVacation && (
                <span className="ml-2 text-xs text-primary-600 dark:text-primary-400">
                  ✈️ Trip
                </span>
              )}
            </h4>
            
            {/* Amount */}
            {bill.amount > 0 && (
              <span className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                ${bill.amount.toFixed(2)}
              </span>
            )}
          </div>

          {/* Due date and status */}
          <div className="flex items-center gap-3 mt-1">
            <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400">
              <Calendar className="w-3 h-3" />
              <span>
                {dueDate 
                  ? format(dueDate, 'MMM d, yyyy')
                  : 'No due date'
                }
              </span>
            </div>

            {/* Status indicator */}
            {isOverdue && (
              <span className="flex items-center gap-1 text-xs text-red-600 dark:text-red-400">
                <AlertCircle className="w-3 h-3" />
                Overdue
              </span>
            )}
            {isDueSoon && !isOverdue && (
              <span className="flex items-center gap-1 text-xs text-yellow-600 dark:text-yellow-400">
                <Clock className="w-3 h-3" />
                Due soon
              </span>
            )}
            {bill.isRecurring && (
              <span className="text-xs text-gray-500 dark:text-gray-400">
                🔄 Recurring
              </span>
            )}
          </div>

          {/* Notes preview */}
          {bill.notes && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">
              {bill.notes}
            </p>
          )}
        </div>

        {/* Action buttons */}
        <AnimatePresence>
          {showActions && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="flex items-center gap-1"
            >
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  // Handle edit
                }}
                className="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
                aria-label="Edit bill"
              >
                <Edit2 className="w-4 h-4" />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onDelete()
                }}
                className="p-1.5 text-gray-400 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors"
                aria-label="Delete bill"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  )
}

export default BillsWidget