import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { FixedSizeList as List } from 'react-window'
import { 
  CheckSquare, 
  Square, 
  Plus, 
  X, 
  Clock,
  ListTodo,
  Trash2,
  Search,
  Filter,
  CheckCircle,
  Circle
} from 'lucide-react'
import { useMutation, useQueryClient } from 'react-query'
import toast from 'react-hot-toast'

import { 
  createTodo, 
  deleteTodo, 
  completeMultipleTodos,
  formatTodoForDisplay, 
  formatTodoAge,
  validateTodoData,
  TODO_LIMITS 
} from '../../api/todos'
import { queryKeys } from '../../api/client'

const TodosWidget = ({ widgetId, data, isEditMode }) => {
  const queryClient = useQueryClient()
  const [newTodoText, setNewTodoText] = useState('')
  const [searchText, setSearchText] = useState('')
  const [selectedTodos, setSelectedTodos] = useState(new Set())
  const [isMultiSelectMode, setIsMultiSelectMode] = useState(false)
  const inputRef = useRef(null)

  // Get todos data
  const todos = (data?.todos || [])
    .map(todo => formatTodoForDisplay(todo))
    .filter(todo => {
      if (searchText) {
        return todo.text.toLowerCase().includes(searchText.toLowerCase())
      }
      return true
    })

  // Create todo mutation
  const createTodoMutation = useMutation(
    (todoData) => createTodo(todoData),
    {
      onSuccess: () => {
        setNewTodoText('')
        queryClient.invalidateQueries(queryKeys.dashboard.all)
        queryClient.invalidateQueries(queryKeys.todos.all)
        toast.success('Todo added')
      },
      onError: () => {
        toast.error('Failed to add todo')
      },
    }
  )

  // Delete todo mutation
  const deleteTodoMutation = useMutation(
    (todoId) => deleteTodo(todoId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(queryKeys.dashboard.all)
        queryClient.invalidateQueries(queryKeys.todos.all)
        if (selectedTodos.has(todoId)) {
          setSelectedTodos(prev => {
            const next = new Set(prev)
            next.delete(todoId)
            return next
          })
        }
      },
      onError: () => {
        toast.error('Failed to complete todo')
      },
    }
  )

  // Handle add todo
  const handleAddTodo = (e) => {
    e.preventDefault()
    
    const validation = validateTodoData({ text: newTodoText })
    if (!validation.isValid) {
      toast.error(validation.errors.text)
      return
    }

    createTodoMutation.mutate({ text: newTodoText })
  }

  // Handle complete todo
  const handleCompleteTodo = (todoId) => {
    deleteTodoMutation.mutate(todoId)
    toast.success('Todo completed!')
  }

  // Handle bulk complete using completeMultipleTodos API
  const handleBulkComplete = () => {
    if (selectedTodos.size === 0) return

    completeMultipleTodos(Array.from(selectedTodos))
      .then(() => {
        // Invalidate todos query to refresh data
        queryClient.invalidateQueries([queryKeys.todos])
        toast.success(`${selectedTodos.size} todos completed!`)
        setSelectedTodos(new Set())
        setIsMultiSelectMode(false)
      })
      .catch((error) => {
        console.error('Bulk complete error:', error)
        toast.error('Failed to complete todos')
      })
  }

  // Toggle select todo
  const toggleSelectTodo = (todoId) => {
    setSelectedTodos(prev => {
      const next = new Set(prev)
      if (next.has(todoId)) {
        next.delete(todoId)
      } else {
        next.add(todoId)
      }
      return next
    })
  }

  // Toggle select all
  const toggleSelectAll = () => {
    if (selectedTodos.size === todos.length) {
      setSelectedTodos(new Set())
    } else {
      setSelectedTodos(new Set(todos.map(t => t.id)))
    }
  }

  // Exit multi-select mode
  const exitMultiSelectMode = () => {
    setIsMultiSelectMode(false)
    setSelectedTodos(new Set())
  }

  // Calculate stats
  const stats = {
    total: todos.length,
    today: todos.filter(t => formatTodoAge(t.createdAt).includes('h ago') || formatTodoAge(t.createdAt) === 'just now').length,
    selected: selectedTodos.size,
  }

  if (todos.length === 0 && !searchText) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-4">
        <ListTodo className="w-12 h-12 text-gray-300 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No todos yet</p>
        <form onSubmit={handleAddTodo} className="mt-3 w-full max-w-xs">
          <input
            ref={inputRef}
            type="text"
            value={newTodoText}
            onChange={(e) => setNewTodoText(e.target.value)}
            placeholder="Add your first todo..."
            className="input w-full text-sm"
            disabled={createTodoMutation.isLoading}
          />
        </form>
      </div>
    )
  }

  const shouldVirtualize = todos.length > TODO_LIMITS.VIRTUALIZATION_THRESHOLD

  return (
    <div className="h-full flex flex-col">
      {/* Header with stats and actions */}
      <div className="mb-3">
        <div className="flex items-center justify-between mb-2">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {stats.total} todos • {stats.today} today
          </div>
          <div className="flex items-center gap-2">
            {isMultiSelectMode ? (
              <>
                <button
                  onClick={toggleSelectAll}
                  className="text-xs text-primary-600 dark:text-primary-400 hover:underline"
                >
                  {selectedTodos.size === todos.length ? 'Deselect all' : 'Select all'}
                </button>
                <button
                  onClick={handleBulkComplete}
                  disabled={selectedTodos.size === 0}
                  className="btn-primary btn-xs"
                >
                  Complete {selectedTodos.size}
                </button>
                <button
                  onClick={exitMultiSelectMode}
                  className="btn-ghost btn-xs"
                >
                  Cancel
                </button>
              </>
            ) : (
              <button
                onClick={() => setIsMultiSelectMode(true)}
                className="text-xs text-primary-600 dark:text-primary-400 hover:underline"
              >
                Select multiple
              </button>
            )}
          </div>
        </div>

        {/* Search bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            placeholder="Search todos..."
            className="input w-full pl-9 text-sm"
          />
          {searchText && (
            <button
              onClick={() => setSearchText('')}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Todos list */}
      <div className="flex-1 overflow-hidden">
        {shouldVirtualize ? (
          <VirtualTodoList
            todos={todos}
            isMultiSelectMode={isMultiSelectMode}
            selectedTodos={selectedTodos}
            onToggleSelect={toggleSelectTodo}
            onComplete={handleCompleteTodo}
          />
        ) : (
          <div className="space-y-1 overflow-auto h-full pr-1">
            <AnimatePresence mode="popLayout">
              {todos.map((todo, index) => (
                <TodoItem
                  key={todo.id}
                  todo={todo}
                  index={index}
                  isMultiSelectMode={isMultiSelectMode}
                  isSelected={selectedTodos.has(todo.id)}
                  onToggleSelect={() => toggleSelectTodo(todo.id)}
                  onComplete={() => handleCompleteTodo(todo.id)}
                />
              ))}
            </AnimatePresence>
          </div>
        )}
      </div>

      {/* Add todo form */}
      <form onSubmit={handleAddTodo} className="mt-3">
        <div className="relative">
          <input
            ref={inputRef}
            type="text"
            value={newTodoText}
            onChange={(e) => setNewTodoText(e.target.value)}
            placeholder="Add a new todo..."
            className="input w-full pr-10"
            disabled={createTodoMutation.isLoading}
          />
          <button
            type="submit"
            disabled={!newTodoText.trim() || createTodoMutation.isLoading}
            className="absolute right-1 top-1/2 -translate-y-1/2 p-2 text-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {createTodoMutation.isLoading ? (
              <span className="w-5 h-5 border-2 border-primary-600 border-t-transparent rounded-full animate-spin block" />
            ) : (
              <Plus className="w-5 h-5" />
            )}
          </button>
        </div>
      </form>
    </div>
  )
}

// Individual todo item component
const TodoItem = ({ todo, index, isMultiSelectMode, isSelected, onToggleSelect, onComplete }) => {
  const age = formatTodoAge(todo.createdAt)

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      transition={{ delay: Math.min(index * 0.03, 0.3) }}
      whileHover={{ x: 2 }}
      className={`
        group flex items-center gap-2 p-2 rounded-lg transition-all cursor-pointer
        ${isSelected 
          ? 'bg-primary-50 dark:bg-primary-900/20 border border-primary-300 dark:border-primary-700' 
          : 'hover:bg-gray-50 dark:hover:bg-gray-800'
        }
      `}
      onClick={() => isMultiSelectMode ? onToggleSelect() : onComplete()}
    >
      {/* Checkbox/Circle */}
      <button
        onClick={(e) => {
          e.stopPropagation()
          isMultiSelectMode ? onToggleSelect() : onComplete()
        }}
        className="flex-shrink-0 text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
      >
        {isMultiSelectMode ? (
          isSelected ? (
            <CheckSquare className="w-5 h-5 text-primary-600 dark:text-primary-400" />
          ) : (
            <Square className="w-5 h-5" />
          )
        ) : (
          <Circle className="w-5 h-5" />
        )}
      </button>

      {/* Todo text */}
      <div className="flex-1 min-w-0">
        <p className="text-sm text-gray-900 dark:text-gray-100 break-words">
          {todo.text}
        </p>
      </div>

      {/* Age */}
      <span className="text-xs text-gray-400 dark:text-gray-500 flex-shrink-0">
        {age}
      </span>

      {/* Complete button */}
      {!isMultiSelectMode && (
        <button
          onClick={(e) => {
            e.stopPropagation()
            onComplete()
          }}
          className="opacity-0 group-hover:opacity-100 p-1 text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20 rounded transition-all"
          aria-label="Complete todo"
        >
          <CheckCircle className="w-4 h-4" />
        </button>
      )}
    </motion.div>
  )
}

// Virtual list for large number of todos
const VirtualTodoList = ({ todos, isMultiSelectMode, selectedTodos, onToggleSelect, onComplete }) => {
  const Row = ({ index, style }) => {
    const todo = todos[index]
    
    return (
      <div style={style}>
        <TodoItem
          todo={todo}
          index={index}
          isMultiSelectMode={isMultiSelectMode}
          isSelected={selectedTodos.has(todo.id)}
          onToggleSelect={() => onToggleSelect(todo.id)}
          onComplete={() => onComplete(todo.id)}
        />
      </div>
    )
  }

  return (
    <List
      height={400} // Adjust based on widget height
      itemCount={todos.length}
      itemSize={48} // Height of each todo item
      width="100%"
    >
      {Row}
    </List>
  )
}

export default TodosWidget