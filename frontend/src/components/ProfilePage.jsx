import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import { useMutation, useQuery, useQueryClient } from 'react-query'
import toast from 'react-hot-toast'
import { 
  User, 
  Mail, 
  Phone, 
  Lock, 
  Save, 
  ArrowLeft,
  Edit2,
  X,
  Check,
  AlertCircle,
  Palette,
  Tag,
  Plus,
  Trash2
} from 'lucide-react'

import { useAuth } from '../utils/useAuth'
import { useTheme } from '../utils/useTheme'
import { apiHelpers, queryKeys } from '../api/client'

// Validation schema
const profileSchema = Yup.object().shape({
  username: Yup.string()
    .min(3, 'Username must be at least 3 characters')
    .max(20, 'Username must be less than 20 characters')
    .required('Username is required'),
  email: Yup.string()
    .email('Invalid email address')
    .nullable(),
  phone: Yup.string()
    .matches(/^[\d\s\-\+\(\)]+$/, 'Invalid phone number')
    .nullable(),
})

const passwordSchema = Yup.object().shape({
  currentPassword: Yup.string()
    .required('Current password is required'),
  newPassword: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .required('New password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('newPassword'), null], 'Passwords must match')
    .required('Please confirm your password'),
})

const ProfilePage = () => {
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { user, updateUser } = useAuth()
  const { theme, toggleTheme } = useTheme()
  const [isEditingPassword, setIsEditingPassword] = useState(false)
  const [isEditingCategories, setIsEditingCategories] = useState(false)
  const [categories, setCategories] = useState([])

  // Fetch user profile
  const { data: profileData, isLoading } = useQuery(
    queryKeys.user.profile,
    () => apiHelpers.get('/user'),
    {
      onSuccess: (data) => {
        setCategories(data.categories || [])
      },
    }
  )

  // Update profile mutation
  const updateProfileMutation = useMutation(
    (data) => apiHelpers.patch('/user', data),
    {
      onSuccess: (data) => {
        toast.success('Profile updated successfully')
        queryClient.invalidateQueries(queryKeys.user.profile)
        updateUser(data)
      },
      onError: () => {
        toast.error('Failed to update profile')
      },
    }
  )

  // Update password mutation
  const updatePasswordMutation = useMutation(
    (data) => apiHelpers.patch('/user', { password: data.newPassword }),
    {
      onSuccess: () => {
        toast.success('Password updated successfully')
        setIsEditingPassword(false)
      },
      onError: () => {
        toast.error('Failed to update password')
      },
    }
  )

  // Handle category changes
  const handleAddCategory = () => {
    const newCategory = {
      id: `custom-${Date.now()}`,
      name: 'New Category',
      color: '#' + Math.floor(Math.random()*16777215).toString(16),
    }
    setCategories([...categories, newCategory])
  }

  const handleUpdateCategory = (index, field, value) => {
    const updated = [...categories]
    updated[index] = { ...updated[index], [field]: value }
    setCategories(updated)
  }

  const handleDeleteCategory = (index) => {
    setCategories(categories.filter((_, i) => i !== index))
  }

  const handleSaveCategories = () => {
    updateProfileMutation.mutate({ categories })
    setIsEditingCategories(false)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-primary-200 dark:border-primary-800 border-t-primary-600 dark:border-t-primary-400 rounded-full animate-spin"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-6">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate('/')}
            className="mb-4 flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Dashboard
          </button>
          
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Profile Settings
          </h1>
          <p className="mt-1 text-gray-600 dark:text-gray-400">
            Manage your account settings and preferences
          </p>
        </div>

        {/* Profile Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6"
        >
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
            Account Information
          </h2>

          <Formik
            initialValues={{
              username: profileData?.username || '',
              email: profileData?.email || '',
              phone: profileData?.phone || '',
            }}
            validationSchema={profileSchema}
            onSubmit={(values, { setSubmitting }) => {
              updateProfileMutation.mutate(values, {
                onSettled: () => setSubmitting(false),
              })
            }}
            enableReinitialize
          >
            {({ errors, touched, isSubmitting }) => (
              <Form className="space-y-4">
                {/* Username */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Username
                  </label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <Field
                      name="username"
                      type="text"
                      className={`input pl-10 ${
                        errors.username && touched.username 
                          ? 'border-error-light focus:ring-error-light' 
                          : ''
                      }`}
                    />
                  </div>
                  {errors.username && touched.username && (
                    <p className="mt-1 text-sm text-error">{errors.username}</p>
                  )}
                </div>

                {/* Email */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Email (optional)
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <Field
                      name="email"
                      type="email"
                      className={`input pl-10 ${
                        errors.email && touched.email 
                          ? 'border-error-light focus:ring-error-light' 
                          : ''
                      }`}
                    />
                  </div>
                  {errors.email && touched.email && (
                    <p className="mt-1 text-sm text-error">{errors.email}</p>
                  )}
                </div>

                {/* Phone */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Phone (optional)
                  </label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <Field
                      name="phone"
                      type="tel"
                      className={`input pl-10 ${
                        errors.phone && touched.phone 
                          ? 'border-error-light focus:ring-error-light' 
                          : ''
                      }`}
                    />
                  </div>
                  {errors.phone && touched.phone && (
                    <p className="mt-1 text-sm text-error">{errors.phone}</p>
                  )}
                </div>

                {/* Submit button */}
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="btn-primary"
                >
                  {isSubmitting ? (
                    <span className="flex items-center">
                      <span className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Saving...
                    </span>
                  ) : (
                    <span className="flex items-center">
                      <Save className="w-4 h-4 mr-2" />
                      Save Changes
                    </span>
                  )}
                </button>
              </Form>
            )}
          </Formik>
        </motion.div>

        {/* Password Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Password
            </h2>
            {!isEditingPassword && (
              <button
                onClick={() => setIsEditingPassword(true)}
                className="btn-outline btn-sm"
              >
                <Edit2 className="w-4 h-4 mr-1" />
                Change
              </button>
            )}
          </div>

          {isEditingPassword ? (
            <Formik
              initialValues={{
                currentPassword: '',
                newPassword: '',
                confirmPassword: '',
              }}
              validationSchema={passwordSchema}
              onSubmit={(values, { setSubmitting, resetForm }) => {
                updatePasswordMutation.mutate(values, {
                  onSuccess: () => {
                    resetForm()
                    setIsEditingPassword(false)
                  },
                  onSettled: () => setSubmitting(false),
                })
              }}
            >
              {({ errors, touched, isSubmitting }) => (
                <Form className="space-y-4">
                  {/* Current Password */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Current Password
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <Field
                        name="currentPassword"
                        type="password"
                        className={`input pl-10 ${
                          errors.currentPassword && touched.currentPassword 
                            ? 'border-error-light focus:ring-error-light' 
                            : ''
                        }`}
                      />
                    </div>
                    {errors.currentPassword && touched.currentPassword && (
                      <p className="mt-1 text-sm text-error">{errors.currentPassword}</p>
                    )}
                  </div>

                  {/* New Password */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      New Password
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <Field
                        name="newPassword"
                        type="password"
                        className={`input pl-10 ${
                          errors.newPassword && touched.newPassword 
                            ? 'border-error-light focus:ring-error-light' 
                            : ''
                        }`}
                      />
                    </div>
                    {errors.newPassword && touched.newPassword && (
                      <p className="mt-1 text-sm text-error">{errors.newPassword}</p>
                    )}
                  </div>

                  {/* Confirm Password */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Confirm New Password
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <Field
                        name="confirmPassword"
                        type="password"
                        className={`input pl-10 ${
                          errors.confirmPassword && touched.confirmPassword 
                            ? 'border-error-light focus:ring-error-light' 
                            : ''
                        }`}
                      />
                    </div>
                    {errors.confirmPassword && touched.confirmPassword && (
                      <p className="mt-1 text-sm text-error">{errors.confirmPassword}</p>
                    )}
                  </div>

                  {/* Action buttons */}
                  <div className="flex gap-3">
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="btn-primary"
                    >
                      {isSubmitting ? 'Updating...' : 'Update Password'}
                    </button>
                    <button
                      type="button"
                      onClick={() => setIsEditingPassword(false)}
                      className="btn-ghost"
                    >
                      Cancel
                    </button>
                  </div>
                </Form>
              )}
            </Formik>
          ) : (
            <p className="text-gray-600 dark:text-gray-400">
              ••••••••
            </p>
          )}
        </motion.div>

        {/* Theme Preferences */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6"
        >
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
            Appearance
          </h2>

          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-700 dark:text-gray-300 font-medium">
                Theme
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Choose your preferred color scheme
              </p>
            </div>
            
            <button
              onClick={toggleTheme}
              className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 dark:bg-gray-700 transition-colors"
            >
              <span className="sr-only">Toggle theme</span>
              <span
                className={`
                  inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                  ${theme === 'dark' ? 'translate-x-6' : 'translate-x-1'}
                `}
              />
            </button>
          </div>
        </motion.div>

        {/* Categories */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Bill Categories
            </h2>
            {!isEditingCategories ? (
              <button
                onClick={() => setIsEditingCategories(true)}
                className="btn-outline btn-sm"
              >
                <Edit2 className="w-4 h-4 mr-1" />
                Edit
              </button>
            ) : (
              <div className="flex gap-2">
                <button
                  onClick={handleSaveCategories}
                  className="btn-primary btn-sm"
                >
                  <Check className="w-4 h-4 mr-1" />
                  Save
                </button>
                <button
                  onClick={() => {
                    setCategories(profileData?.categories || [])
                    setIsEditingCategories(false)
                  }}
                  className="btn-ghost btn-sm"
                >
                  <X className="w-4 h-4 mr-1" />
                  Cancel
                </button>
              </div>
            )}
          </div>

          <div className="space-y-2">
            {categories.map((category, index) => (
              <div
                key={category.id}
                className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50"
              >
                {isEditingCategories ? (
                  <>
                    <input
                      type="color"
                      value={category.color}
                      onChange={(e) => handleUpdateCategory(index, 'color', e.target.value)}
                      className="w-8 h-8 rounded cursor-pointer"
                    />
                    <input
                      type="text"
                      value={category.name}
                      onChange={(e) => handleUpdateCategory(index, 'name', e.target.value)}
                      className="input flex-1"
                    />
                    <button
                      onClick={() => handleDeleteCategory(index)}
                      className="p-1 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </>
                ) : (
                  <>
                    <div
                      className="w-4 h-4 rounded"
                      style={{ backgroundColor: category.color }}
                    />
                    <span className="text-gray-700 dark:text-gray-300">
                      {category.name}
                    </span>
                  </>
                )}
              </div>
            ))}
            
            {isEditingCategories && (
              <button
                onClick={handleAddCategory}
                className="w-full mt-2 p-2 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-600 dark:text-gray-400 hover:border-gray-400 dark:hover:border-gray-500 transition-colors"
              >
                <Plus className="w-4 h-4 mx-auto" />
              </button>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default ProfilePage