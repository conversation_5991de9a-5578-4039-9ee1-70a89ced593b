import { apiHelpers } from './client'

// Authentication API endpoints
export const authApi = {
  // Login user
  login: async (credentials) => {
    const response = await apiHelpers.post('/auth/login', credentials)
    return response.data || response
  },

  // Logout user
  logout: async () => {
    const response = await apiHelpers.post('/auth/logout')
    return response.data || response
  },

  // Check authentication status
  check: async () => {
    const response = await apiHelpers.get('/auth/check')
    return response.data || response
  },
}

// Export individual functions for easier imports
export const login = authApi.login
export const logout = authApi.logout
export const checkAuth = authApi.check

// Helper function to validate login credentials
export const validateLoginCredentials = (username, password) => {
  const errors = {}

  if (!username || username.trim().length === 0) {
    errors.username = 'Username is required'
  }

  if (!password || password.length === 0) {
    errors.password = 'Password is required'
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  }
}

// Helper function to handle login success
export const handleLoginSuccess = (userData, navigate, from) => {
  // Navigate to the page user was trying to access or dashboard
  const destination = from?.pathname || '/'
  navigate(destination, { replace: true })
}

// Helper function to handle logout
export const handleLogout = async (navigate, clearUserData) => {
  try {
    await logout()
  } catch (error) {
    // Even if logout fails on server, clear local state
    console.error('Logout error:', error)
  } finally {
    // Clear user data from store
    clearUserData()
    
    // Navigate to login
    navigate('/login', { replace: true })
  }
}