import { apiHelpers } from './client'

// Events API endpoints
export const eventsApi = {
  // Get all events for the user
  getAll: async () => {
    const response = await apiHelpers.get('/events')
    return response.data || response
  },

  // Get a specific event by ID
  getById: async (id) => {
    const response = await apiHelpers.get(`/events/${id}`)
    return response.data || response
  },

  // Create a new event
  create: async (eventData) => {
    const response = await apiHelpers.post('/events', eventData)
    return response.data || response
  },

  // Update an existing event
  update: async (id, eventData) => {
    const response = await apiHelpers.patch(`/events/${id}`, eventData)
    return response.data || response
  },

  // Delete an event
  delete: async (id) => {
    const response = await apiHelpers.delete(`/events/${id}`)
    return response.data || response
  },

  // Get upcoming events (includes expanded recurring events)
  getUpcoming: async (limit = 10) => {
    const response = await apiHelpers.get(`/events/upcoming?limit=${limit}`)
    return response.data || response
  },
}

// Export individual functions
export const getAllEvents = eventsApi.getAll
export const getEventById = eventsApi.getById
export const createEvent = eventsApi.create
export const updateEvent = eventsApi.update
export const deleteEvent = eventsApi.delete
export const getUpcomingEvents = eventsApi.getUpcoming

// Helper function to format event data for API
export const formatEventForApi = (formData) => {
  const apiData = {
    title: formData.title?.trim(),
    start: formData.startDate ? new Date(formData.startDate).toISOString() : null,
    end: formData.endDate ? new Date(formData.endDate).toISOString() : null,
    notes: formData.notes?.trim() || null,
  }

  // Handle recurrence
  if (formData.isRecurring) {
    apiData.rrule = formData.rrule
  } else {
    apiData.rrule = null
  }

  return apiData
}

// Helper function to format event from API for display
export const formatEventForDisplay = (event) => {
  return {
    id: event._id,
    title: event.title,
    startDate: event.start ? new Date(event.start) : null,
    endDate: event.end ? new Date(event.end) : null,
    notes: event.notes || '',
    isRecurring: !!event.rrule,
    rrule: event.rrule,
    createdAt: new Date(event.created_at),
    updatedAt: new Date(event.updated_at),
  }
}

// Helper function to validate event form data
export const validateEventData = (formData) => {
  const errors = {}

  if (!formData.title || formData.title.trim().length === 0) {
    errors.title = 'Title is required'
  }

  if (!formData.startDate) {
    errors.startDate = 'Start date is required'
  }

  if (!formData.endDate) {
    errors.endDate = 'End date is required'
  }

  if (formData.startDate && formData.endDate) {
    const start = new Date(formData.startDate)
    const end = new Date(formData.endDate)
    
    if (end < start) {
      errors.endDate = 'End date must be after start date'
    }
  }

  if (formData.isRecurring && !formData.rrule) {
    errors.rrule = 'Recurrence rule is required for recurring events'
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  }
}

// Helper function to check if an event is happening now
export const isEventActive = (event) => {
  const now = new Date()
  const start = new Date(event.start)
  const end = new Date(event.end)
  
  return now >= start && now <= end
}

// Helper function to check if an event is in the past
export const isEventPast = (event) => {
  const now = new Date()
  const end = new Date(event.end)
  
  return end < now
}

// Helper function to check if an event is in the future
export const isEventFuture = (event) => {
  const now = new Date()
  const start = new Date(event.start)
  
  return start > now
}

// Helper function to format event duration
export const formatEventDuration = (event) => {
  if (!event.start || !event.end) return ''
  
  const start = new Date(event.start)
  const end = new Date(event.end)
  const durationMs = end - start
  
  const hours = Math.floor(durationMs / (1000 * 60 * 60))
  const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours > 0 && minutes > 0) {
    return `${hours}h ${minutes}m`
  } else if (hours > 0) {
    return `${hours}h`
  } else if (minutes > 0) {
    return `${minutes}m`
  } else {
    return '< 1m'
  }
}

// Helper function to get event color based on status
export const getEventColor = (event) => {
  if (isEventActive(event)) {
    return 'primary' // Blue for active
  } else if (isEventPast(event)) {
    return 'gray' // Gray for past
  } else {
    return 'success' // Green for future
  }
}

// Helper function to create calendar event data
export const createCalendarEvent = (event) => {
  return {
    id: event.id,
    title: event.title,
    start: event.startDate,
    end: event.endDate,
    allDay: false,
    color: getEventColor(event),
    extendedProps: {
      notes: event.notes,
      isRecurring: event.isRecurring,
      rrule: event.rrule,
    },
  }
}