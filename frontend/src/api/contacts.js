import { apiHelpers } from './client'

// Contacts API endpoints
export const contactsApi = {
  // Get all contacts for the user
  getAll: async () => {
    const response = await apiHelpers.get('/contacts')
    return response.data || response
  },

  // Get a specific contact by ID
  getById: async (id) => {
    const response = await apiHelpers.get(`/contacts/${id}`)
    return response.data || response
  },

  // Create a new contact
  create: async (contactData) => {
    const response = await apiHelpers.post('/contacts', contactData)
    return response.data || response
  },

  // Update an existing contact
  update: async (id, contactData) => {
    const response = await apiHelpers.patch(`/contacts/${id}`, contactData)
    return response.data || response
  },

  // Delete a contact
  delete: async (id) => {
    const response = await apiHelpers.delete(`/contacts/${id}`)
    return response.data || response
  },

  // Update last contact date (ping)
  ping: async (id) => {
    const response = await apiHelpers.post(`/contacts/${id}/ping`)
    return response.data || response
  },

  // Get contact types
  getTypes: async () => {
    const response = await apiHelpers.get('/contacts/types')
    return response.data || response
  },

  // Create a new contact type
  createType: async (typeData) => {
    const response = await apiHelpers.post('/contacts/types', typeData)
    return response.data || response
  },

  // Update a contact type
  updateType: async (id, typeData) => {
    const response = await apiHelpers.patch(`/contacts/types/${id}`, typeData)
    return response.data || response
  },

  // Delete a contact type
  deleteType: async (id) => {
    const response = await apiHelpers.delete(`/contacts/types/${id}`)
    return response.data || response
  },
}

// Export individual functions
export const getAllContacts = contactsApi.getAll
export const getContactById = contactsApi.getById
export const createContact = contactsApi.create
export const updateContact = contactsApi.update
export const deleteContact = contactsApi.delete
export const pingContact = contactsApi.ping
export const getContactTypes = contactsApi.getTypes
export const createContactType = contactsApi.createType
export const updateContactType = contactsApi.updateType
export const deleteContactType = contactsApi.deleteType

// Helper function to format contact data for API
export const formatContactForApi = (formData) => {
  return {
    name: formData.name?.trim(),
    type_id: formData.typeId,
    last_contact: formData.lastContact ? new Date(formData.lastContact).toISOString() : null,
    notes: formData.notes?.trim() || null,
  }
}

// Helper function to format contact from API for display
export const formatContactForDisplay = (contact) => {
  return {
    id: contact._id,
    name: contact.name,
    typeId: contact.type_id,
    typeName: contact.type?.label || '',
    lastContact: contact.last_contact ? new Date(contact.last_contact) : null,
    notes: contact.notes || '',
    status: contact.status || 'green',
    daysSinceContact: contact.days_since_contact || 0,
    thresholdDays: contact.type?.threshold_days || 7,
    createdAt: new Date(contact.created_at),
    updatedAt: new Date(contact.updated_at),
  }
}

// Helper function to validate contact form data
export const validateContactData = (formData) => {
  const errors = {}

  if (!formData.name || formData.name.trim().length === 0) {
    errors.name = 'Name is required'
  }

  if (!formData.typeId) {
    errors.typeId = 'Contact type is required'
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  }
}

// Helper function to validate contact type form data
export const validateContactTypeData = (formData) => {
  const errors = {}

  if (!formData.label || formData.label.trim().length === 0) {
    errors.label = 'Label is required'
  }

  if (!formData.thresholdDays || formData.thresholdDays < 1) {
    errors.thresholdDays = 'Threshold must be at least 1 day'
  }

  if (![3, 7, 21].includes(parseInt(formData.thresholdDays))) {
    errors.thresholdDays = 'Threshold must be 3, 7, or 21 days'
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  }
}

// Helper function to get contact status color
export const getContactStatusColor = (contact) => {
  switch (contact.status) {
    case 'green':
      return {
        bg: 'bg-success-light/20 dark:bg-success-dark/20',
        text: 'text-success-dark dark:text-success-light',
        badge: 'badge-success',
      }
    case 'yellow':
      return {
        bg: 'bg-warning-light/20 dark:bg-warning-dark/20',
        text: 'text-warning-dark dark:text-warning-light',
        badge: 'badge-warning',
      }
    case 'red':
      return {
        bg: 'bg-error-light/20 dark:bg-error-dark/20',
        text: 'text-error-dark dark:text-error-light',
        badge: 'badge-error',
      }
    default:
      return {
        bg: 'bg-gray-100 dark:bg-gray-800',
        text: 'text-gray-600 dark:text-gray-400',
        badge: '',
      }
  }
}

// Helper function to format last contact display
export const formatLastContactDisplay = (lastContact, daysSince) => {
  if (!lastContact) {
    return 'Never contacted'
  }

  const days = daysSince || 0

  if (days === 0) {
    return 'Today'
  } else if (days === 1) {
    return 'Yesterday'
  } else if (days < 7) {
    return `${days} days ago`
  } else if (days < 30) {
    const weeks = Math.floor(days / 7)
    return `${weeks} ${weeks === 1 ? 'week' : 'weeks'} ago`
  } else if (days < 365) {
    const months = Math.floor(days / 30)
    return `${months} ${months === 1 ? 'month' : 'months'} ago`
  } else {
    const years = Math.floor(days / 365)
    return `${years} ${years === 1 ? 'year' : 'years'} ago`
  }
}

// Helper function to sort contacts by urgency
export const sortContactsByUrgency = (contacts) => {
  const statusOrder = { red: 0, yellow: 1, green: 2 }
  
  return [...contacts].sort((a, b) => {
    // First sort by status
    const statusDiff = statusOrder[a.status] - statusOrder[b.status]
    if (statusDiff !== 0) return statusDiff
    
    // Then by days since contact (descending)
    return (b.daysSinceContact || 0) - (a.daysSinceContact || 0)
  })
}

// Helper function to group contacts by type
export const groupContactsByType = (contacts) => {
  return contacts.reduce((groups, contact) => {
    const typeName = contact.typeName || 'Other'
    if (!groups[typeName]) {
      groups[typeName] = []
    }
    groups[typeName].push(contact)
    return groups
  }, {})
}