import { apiHelpers } from './client'

// Weather API endpoints
export const weatherApi = {
  // Get weather for a specific city
  getByCity: async (city) => {
    const response = await apiHelpers.get(`/weather?city=${city}`)
    return response.data || response
  },

  // Get weather for all configured cities
  getAll: async () => {
    const cities = ['stl', 'accra']
    const weatherData = {}
    
    // Fetch weather for each city in parallel
    const promises = cities.map(city => 
      weatherApi.getByCity(city)
        .then(data => ({ city, data }))
        .catch(error => ({ city, error }))
    )
    
    const results = await Promise.all(promises)
    
    // Process results
    results.forEach(({ city, data, error }) => {
      if (!error) {
        weatherData[city] = data
      } else {
        weatherData[city] = null
      }
    })
    
    return weatherData
  },

  // Force refresh weather data (bypasses cache)
  refresh: async (city) => {
    const response = await apiHelpers.post(`/weather/refresh?city=${city}`)
    return response.data || response
  },
}

// Export individual functions
export const getWeatherByCity = weatherApi.getByCity
export const getAllWeather = weatherApi.getAll
export const refreshWeather = weatherApi.refresh

// City configurations
export const WEATHER_CITIES = {
  stl: {
    code: 'stl',
    name: 'St. Louis',
    state: 'MO',
    country: 'USA',
    timezone: 'America/Chicago',
  },
  accra: {
    code: 'accra',
    name: 'Accra',
    state: null,
    country: 'Ghana',
    timezone: 'Africa/Accra',
  },
}

// Weather condition mappings
export const WEATHER_CONDITIONS = {
  // Clear
  'Clear': { icon: 'sun', color: 'yellow' },
  'Mainly Clear': { icon: 'sun', color: 'yellow' },
  
  // Cloudy
  'Partly Cloudy': { icon: 'cloud-sun', color: 'gray' },
  'Overcast': { icon: 'cloud', color: 'gray' },
  
  // Fog
  'Foggy': { icon: 'cloud-fog', color: 'gray' },
  'Depositing Rime Fog': { icon: 'cloud-fog', color: 'gray' },
  
  // Drizzle
  'Light Drizzle': { icon: 'cloud-drizzle', color: 'blue' },
  'Moderate Drizzle': { icon: 'cloud-drizzle', color: 'blue' },
  'Dense Drizzle': { icon: 'cloud-drizzle', color: 'blue' },
  
  // Rain
  'Slight Rain': { icon: 'cloud-rain', color: 'blue' },
  'Moderate Rain': { icon: 'cloud-rain', color: 'blue' },
  'Heavy Rain': { icon: 'cloud-rain', color: 'blue' },
  
  // Snow
  'Slight Snow': { icon: 'cloud-snow', color: 'white' },
  'Moderate Snow': { icon: 'cloud-snow', color: 'white' },
  'Heavy Snow': { icon: 'cloud-snow', color: 'white' },
  
  // Showers
  'Slight Rain Showers': { icon: 'cloud-rain', color: 'blue' },
  'Moderate Rain Showers': { icon: 'cloud-rain', color: 'blue' },
  'Violent Rain Showers': { icon: 'cloud-rain', color: 'blue' },
  
  // Thunderstorm
  'Thunderstorm': { icon: 'cloud-lightning', color: 'purple' },
  'Thunderstorm with Hail': { icon: 'cloud-lightning', color: 'purple' },
  'Thunderstorm with Heavy Hail': { icon: 'cloud-lightning', color: 'purple' },
  
  // Default
  'Unknown': { icon: 'help-circle', color: 'gray' },
}

// Helper function to get weather icon component props
export const getWeatherIconProps = (condition) => {
  const config = WEATHER_CONDITIONS[condition] || WEATHER_CONDITIONS['Unknown']
  return {
    name: config.icon,
    className: `text-${config.color}-500`,
  }
}

// Helper function to format temperature
export const formatTemperature = (temp, unit = 'F') => {
  if (temp === null || temp === undefined) return '--'
  
  const rounded = Math.round(temp)
  return `${rounded}°${unit}`
}

// Helper function to format weather data for display
export const formatWeatherForDisplay = (weatherData) => {
  if (!weatherData) return null
  
  return {
    current: {
      temperature: weatherData.current?.temperature,
      temperatureDisplay: formatTemperature(weatherData.current?.temperature),
      condition: weatherData.current?.condition || 'Unknown',
      icon: weatherData.current?.icon || 'help-circle',
    },
    forecast: weatherData.forecast?.map(day => ({
      date: new Date(day.date),
      high: day.high,
      low: day.low,
      highDisplay: formatTemperature(day.high),
      lowDisplay: formatTemperature(day.low),
      condition: day.condition || 'Unknown',
      icon: day.icon || 'help-circle',
    })) || [],
  }
}

// Helper function to check if weather data is stale
export const isWeatherStale = (fetchedAt, maxAgeHours = 2) => {
  if (!fetchedAt) return true
  
  const now = new Date()
  const fetched = new Date(fetchedAt)
  const ageHours = (now - fetched) / (1000 * 60 * 60)
  
  return ageHours > maxAgeHours
}

// Helper function to get weather alert level
export const getWeatherAlertLevel = (condition) => {
  const severeConditions = [
    'Heavy Rain',
    'Heavy Snow',
    'Violent Rain Showers',
    'Thunderstorm',
    'Thunderstorm with Hail',
    'Thunderstorm with Heavy Hail',
  ]
  
  if (severeConditions.includes(condition)) {
    return 'severe'
  }
  
  const moderateConditions = [
    'Moderate Rain',
    'Moderate Snow',
    'Moderate Rain Showers',
    'Dense Drizzle',
  ]
  
  if (moderateConditions.includes(condition)) {
    return 'moderate'
  }
  
  return 'normal'
}

// Helper function to get weather background gradient
export const getWeatherGradient = (condition) => {
  const config = WEATHER_CONDITIONS[condition] || WEATHER_CONDITIONS['Unknown']
  
  switch (config.color) {
    case 'yellow':
      return 'from-yellow-400 to-orange-500'
    case 'blue':
      return 'from-blue-400 to-blue-600'
    case 'gray':
      return 'from-gray-400 to-gray-600'
    case 'white':
      return 'from-gray-200 to-gray-400'
    case 'purple':
      return 'from-purple-500 to-purple-700'
    default:
      return 'from-gray-400 to-gray-600'
  }
}

// Helper function to format forecast day
export const formatForecastDay = (date) => {
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)
  
  const forecastDate = new Date(date)
  
  // Reset time for comparison
  today.setHours(0, 0, 0, 0)
  tomorrow.setHours(0, 0, 0, 0)
  forecastDate.setHours(0, 0, 0, 0)
  
  if (forecastDate.getTime() === today.getTime()) {
    return 'Today'
  } else if (forecastDate.getTime() === tomorrow.getTime()) {
    return 'Tomorrow'
  } else {
    // Return day of week
    return forecastDate.toLocaleDateString('en-US', { weekday: 'short' })
  }
}