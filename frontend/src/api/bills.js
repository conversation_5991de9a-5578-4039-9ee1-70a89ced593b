import { apiHelpers } from './client'

// Bills API endpoints
export const billsApi = {
  // Get all bills for the user
  getAll: async () => {
    const response = await apiHelpers.get('/bills')
    return response.data || response
  },

  // Get a specific bill by ID
  getById: async (id) => {
    const response = await apiHelpers.get(`/bills/${id}`)
    return response.data || response
  },

  // Create a new bill
  create: async (billData) => {
    const response = await apiHelpers.post('/bills', billData)
    return response.data || response
  },

  // Update an existing bill
  update: async (id, billData) => {
    const response = await apiHelpers.patch(`/bills/${id}`, billData)
    return response.data || response
  },

  // Delete a bill
  delete: async (id) => {
    const response = await apiHelpers.delete(`/bills/${id}`)
    return response.data || response
  },

  // Get upcoming bills (includes expanded recurring bills)
  getUpcoming: async (limit = 20) => {
    const response = await apiHelpers.get(`/bills/upcoming?limit=${limit}`)
    return response.data || response
  },

  // Get bills grouped by category
  getByCategory: async () => {
    const response = await apiHelpers.get('/bills/by-category')
    return response.data || response
  },
}

// Export individual functions
export const getAllBills = billsApi.getAll
export const getBillById = billsApi.getById
export const createBill = billsApi.create
export const updateBill = billsApi.update
export const deleteBill = billsApi.delete
export const getUpcomingBills = billsApi.getUpcoming
export const getBillsByCategory = billsApi.getByCategory

// Helper function to format bill data for API
export const formatBillForApi = (formData) => {
  const apiData = {
    title: formData.title?.trim(),
    amount: formData.amount ? parseFloat(formData.amount) : null,
    category: formData.category || null,
    notes: formData.notes?.trim() || null,
  }

  // Handle due date for one-time bills/vacations
  if (formData.isRecurring) {
    apiData.rrule = formData.rrule
    apiData.due_date = null
  } else {
    apiData.due_date = formData.dueDate ? new Date(formData.dueDate).toISOString() : null
    apiData.rrule = null
  }

  return apiData
}

// Helper function to format bill from API for display
export const formatBillForDisplay = (bill) => {
  return {
    id: bill._id,
    title: bill.title,
    amount: bill.amount || 0,
    category: bill.category,
    notes: bill.notes || '',
    isRecurring: !!bill.rrule,
    rrule: bill.rrule,
    dueDate: bill.due_date ? new Date(bill.due_date) : null,
    createdAt: new Date(bill.created_at),
    updatedAt: new Date(bill.updated_at),
  }
}

// Helper function to validate bill form data
export const validateBillData = (formData) => {
  const errors = {}

  if (!formData.title || formData.title.trim().length === 0) {
    errors.title = 'Title is required'
  }

  if (formData.amount !== null && formData.amount !== undefined) {
    const amount = parseFloat(formData.amount)
    if (isNaN(amount) || amount < 0) {
      errors.amount = 'Amount must be a positive number'
    }
  }

  if (!formData.isRecurring && !formData.dueDate) {
    errors.dueDate = 'Due date is required for one-time bills'
  }

  if (formData.isRecurring && !formData.rrule) {
    errors.rrule = 'Recurrence rule is required for recurring bills'
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  }
}

// Helper function to generate RRULE strings
export const generateRRule = (frequency, interval = 1, count = null, until = null) => {
  let rrule = `FREQ=${frequency}`
  
  if (interval > 1) {
    rrule += `;INTERVAL=${interval}`
  }
  
  if (count) {
    rrule += `;COUNT=${count}`
  } else if (until) {
    rrule += `;UNTIL=${until.toISOString().split('T')[0].replace(/-/g, '')}`
  }
  
  return rrule
}

// Common RRULE templates
export const RRULE_TEMPLATES = {
  DAILY: 'FREQ=DAILY',
  WEEKLY: 'FREQ=WEEKLY',
  BIWEEKLY: 'FREQ=WEEKLY;INTERVAL=2',
  MONTHLY: 'FREQ=MONTHLY',
  QUARTERLY: 'FREQ=MONTHLY;INTERVAL=3',
  YEARLY: 'FREQ=YEARLY',
}

// Helper to check if a bill is overdue
export const isBillOverdue = (bill) => {
  if (!bill.due_date) return false
  
  const dueDate = new Date(bill.due_date)
  const now = new Date()
  
  return dueDate < now
}

// Helper to calculate next due date for recurring bills
export const getNextDueDate = (rrule, startDate = new Date()) => {
  // This would use a proper RRULE parser in production
  // For now, return a placeholder
  return new Date(startDate.getTime() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
}