import { apiHelpers } from './client'

// Todos API endpoints
export const todosApi = {
  // Get all todos for the user
  getAll: async (limit = null) => {
    const url = limit ? `/todos?limit=${limit}` : '/todos'
    const response = await apiHelpers.get(url)
    return response.data || response
  },

  // Get a specific todo by ID
  getById: async (id) => {
    const response = await apiHelpers.get(`/todos/${id}`)
    return response.data || response
  },

  // Create a new todo
  create: async (todoData) => {
    const response = await apiHelpers.post('/todos', todoData)
    return response.data || response
  },

  // Update an existing todo
  update: async (id, todoData) => {
    const response = await apiHelpers.patch(`/todos/${id}`, todoData)
    return response.data || response
  },

  // Delete a todo (mark as completed)
  delete: async (id) => {
    const response = await apiHelpers.delete(`/todos/${id}`)
    return response.data || response
  },

  // Complete multiple todos at once
  completeMultiple: async (ids) => {
    const response = await apiHelpers.post('/todos/complete-multiple', { ids })
    return response.data || response
  },

  // Get todos count
  getCount: async () => {
    const response = await apiHelpers.get('/todos/count')
    return response.data || response
  },
}

// Export individual functions
export const getAllTodos = todosApi.getAll
export const getTodoById = todosApi.getById
export const createTodo = todosApi.create
export const updateTodo = todosApi.update
export const deleteTodo = todosApi.delete
export const completeMultipleTodos = todosApi.completeMultiple
export const getTodosCount = todosApi.getCount

// Helper function to format todo data for API
export const formatTodoForApi = (formData) => {
  return {
    text: formData.text?.trim(),
  }
}

// Helper function to format todo from API for display
export const formatTodoForDisplay = (todo) => {
  return {
    id: todo._id,
    text: todo.text,
    createdAt: new Date(todo.created_at),
  }
}

// Helper function to validate todo form data
export const validateTodoData = (formData) => {
  const errors = {}

  if (!formData.text || formData.text.trim().length === 0) {
    errors.text = 'Todo text is required'
  }

  if (formData.text && formData.text.trim().length > 500) {
    errors.text = 'Todo text must be less than 500 characters'
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  }
}

// Helper function to format todo age
export const formatTodoAge = (createdAt) => {
  const now = new Date()
  const created = new Date(createdAt)
  const diffMs = now - created
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMins < 1) {
    return 'just now'
  } else if (diffMins < 60) {
    return `${diffMins}m ago`
  } else if (diffHours < 24) {
    return `${diffHours}h ago`
  } else if (diffDays < 7) {
    return `${diffDays}d ago`
  } else {
    const weeks = Math.floor(diffDays / 7)
    return `${weeks}w ago`
  }
}

// Helper function to sort todos by creation date
export const sortTodosByDate = (todos, order = 'desc') => {
  return [...todos].sort((a, b) => {
    const dateA = new Date(a.createdAt)
    const dateB = new Date(b.createdAt)
    
    if (order === 'desc') {
      return dateB - dateA // Newest first
    } else {
      return dateA - dateB // Oldest first
    }
  })
}

// Helper function to filter todos by search text
export const filterTodosByText = (todos, searchText) => {
  if (!searchText || searchText.trim().length === 0) {
    return todos
  }

  const search = searchText.toLowerCase().trim()
  return todos.filter(todo => 
    todo.text.toLowerCase().includes(search)
  )
}

// Helper function to get todo statistics
export const getTodoStats = (todos) => {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
  const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)

  return {
    total: todos.length,
    today: todos.filter(todo => new Date(todo.createdAt) >= today).length,
    thisWeek: todos.filter(todo => new Date(todo.createdAt) >= weekAgo).length,
    thisMonth: todos.filter(todo => new Date(todo.createdAt) >= monthAgo).length,
    oldest: todos.length > 0 ? 
      Math.floor((now - new Date(todos[todos.length - 1].createdAt)) / (1000 * 60 * 60 * 24)) : 
      0,
  }
}

// Helper function to batch todos for virtualization
export const batchTodos = (todos, batchSize = 50) => {
  const batches = []
  for (let i = 0; i < todos.length; i += batchSize) {
    batches.push(todos.slice(i, i + batchSize))
  }
  return batches
}

// Helper function to handle quick add (with keyboard shortcuts)
export const parseQuickAdd = (text) => {
  // Parse special commands or tags
  const tags = []
  const cleanText = text
    .replace(/#(\w+)/g, (match, tag) => {
      tags.push(tag)
      return ''
    })
    .trim()

  return {
    text: cleanText,
    tags, // For future enhancement
  }
}

// Constants for todo limits
export const TODO_LIMITS = {
  INITIAL_LOAD: 50,
  LOAD_MORE: 50,
  VIRTUALIZATION_THRESHOLD: 300,
  MAX_DISPLAY: 1000,
}