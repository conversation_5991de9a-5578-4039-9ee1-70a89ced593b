{"name": "personal-organizer-frontend", "private": true, "version": "1.0.0", "type": "module", "description": "A world-class personal life management application with Apple-level design", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.21.1", "axios": "^1.6.5", "react-query": "^3.39.3", "react-grid-layout": "^1.4.4", "react-window": "^1.8.10", "recharts": "^2.10.4", "lucide-react": "^0.303.0", "date-fns": "^3.0.6", "date-fns-tz": "^2.0.0", "formik": "^2.4.5", "yup": "^1.3.3", "clsx": "^2.1.0", "tailwind-merge": "^2.2.0", "zustand": "^4.4.7", "react-hot-toast": "^2.4.1", "framer-motion": "^10.17.9"}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "vite": "^5.0.10", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.33", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}