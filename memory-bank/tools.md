# Personal Organizer - Available Tools & MCPs

## Discovered Tools & MCPs

### Sequential Thinking MCP ✅
- **Purpose**: Step-by-step planning and analysis
- **Usage**: For breaking down complex tasks and decision-making
- **Status**: ✅ Verified and used for project reorganization
- **When to use**: Planning, problem-solving, multi-step analysis

### Context7 MCP ✅  
- **Purpose**: Codebase research and pattern discovery
- **Usage**: Finding relevant code patterns and documentation
- **Status**: ✅ Verified and used for Flask project structure research
- **When to use**: Research existing patterns, find similar implementations

### Web Search MCP ✅
- **Purpose**: Current information and best practices research
- **Usage**: Finding up-to-date development practices and solutions
- **Status**: ✅ Verified and used for React Flask 2025 best practices
- **When to use**: Current best practices, recent changes in technology

### File System Tools ✅
- **LS**: Directory listing and file discovery
- **Read**: File content reading and analysis
- **Write**: File creation and content writing
- **Edit**: Targeted file modifications
- **MultiEdit**: Multiple edits in single file
- **Glob**: Pattern-based file finding
- **Grep**: Content search across files

### Task Management Tools ✅
- **TodoWrite**: Task planning and progress tracking
- **TodoRead**: Current task status review
- **Task**: Agent delegation for complex searches

### Development Tools ✅
- **Bash**: Command execution for development tasks
- **NotebookRead/Edit**: Jupyter notebook handling
- **WebFetch**: Web content retrieval and analysis

### Browser Automation Tools ✅
- **Playwright MCPs**: Complete browser automation suite
  - Navigation, screenshots, interaction
  - Form filling and testing
  - Page snapshots and console monitoring

## Tool Usage Patterns for Personal Organizer

### Development Workflow Tools
1. **File Operations**: Read, Edit, MultiEdit for code changes
2. **System Commands**: Bash for npm/pip installs, server startup
3. **Content Search**: Grep, Glob for finding specific files/patterns
4. **Task Management**: TodoWrite/TodoRead for progress tracking

### Testing and Verification Tools
1. **Bash**: Running application servers and install commands
2. **Playwright**: Browser automation for UI testing
3. **WebFetch**: Verifying external API integrations
4. **LS/Read**: File system verification and content validation

### Research and Planning Tools
1. **Sequential Thinking**: Breaking down complex implementation tasks
2. **Context7**: Finding relevant code patterns and documentation
3. **Web Search**: Current best practices and troubleshooting
4. **Task**: Delegating complex search and analysis tasks

## Tool Priorities for Current Phase

### High Priority (Immediate Use)
- **Bash**: Installing dependencies and starting servers
- **Read**: Verifying configuration files and code
- **TodoWrite**: Tracking application startup progress
- **Playwright**: Testing application functionality

### Medium Priority (Validation Phase)
- **Grep**: Finding and fixing import paths if needed
- **Edit**: Making configuration adjustments
- **WebFetch**: Testing weather API integration
- **LS**: Verifying file structure integrity

### Low Priority (Future Enhancement)
- **Context7**: Research for new features
- **Web Search**: Latest best practices for optimization
- **Task**: Complex feature implementation planning

## MCP Server Commands

### Sequential Thinking
```bash
npx -y @modelcontextprotocol/server-sequential-thinking
```

### Context7
```bash
npx -y @upostack/context7-mcp
```

### Playwright
```bash
npx -y @executeautomation/playwright-mcp-server
```

## Tool Integration Strategy

### Error Handling Protocol
1. **Detection**: Any error triggers Holy Trinity protocol
2. **Analysis**: Sequential Thinking for error understanding
3. **Research**: Context7 + Web Search for solutions
4. **Implementation**: File tools + Bash for fixes
5. **Verification**: Playwright for testing results

### Development Cycle
1. **Planning**: Sequential Thinking + TodoWrite
2. **Research**: Context7 + Web Search when needed
3. **Implementation**: File tools + Bash
4. **Testing**: Playwright + manual verification
5. **Documentation**: Update progress.md and memory bank