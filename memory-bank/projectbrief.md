# Personal Organizer - Project Brief

## Core Requirements and Goals

**Personal Organizer** is a full-stack dashboard application for personal life management with the following core components:

### Technical Stack
- **Frontend**: React 18 + Vite + Tailwind CSS with drag-and-drop dashboard
- **Backend**: Flask + MongoDB with single-user authentication  
- **Architecture**: Widget-based dashboard with real-time data updates

### Core Features
1. **Dashboard Management**: Drag-and-drop widget layout with responsive grid
2. **Weather Widget**: St. Louis & Accra weather display with forecasts
3. **Bills Management**: Recurring and one-time bills with RRULE support
4. **Todo Management**: Unlimited todos with auto-cleanup after 24 hours
5. **Events Calendar**: Calendar events with recurrence patterns
6. **Contacts Tracking**: Relationship management with color-coded thresholds
7. **Data Visualization**: Bills charts using Recharts library

### Authentication & Security
- Single-user system (no registration required)
- One admin user configured via environment variables
- Flask-Login session management
- Rate limiting on authentication endpoints

### Data Management
- MongoDB database with optimized indexes
- UTC timezone storage with America/Chicago display
- TTL indexes for automatic cleanup (weather cache, completed todos)
- User-defined categories with custom hex colors

### Key Success Metrics
- 100% responsive design across all screen sizes
- Real-time data updates without page refresh
- Secure single-user authentication
- Proper timezone handling for all dates
- Efficient data caching and cleanup