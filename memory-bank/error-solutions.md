# Error Solutions Database

## Error: XSS vulnerability in sanitize_input function
**First Encountered**: Security audit on 2025-06-24
**Status**: ✅ RESOLVED on 2025-06-24
**Root Cause**: Custom XSS sanitization function used basic string replacement that could be easily bypassed
**Location Fixed**: `backend/utils.py:262-287`
**Solution Applied**: 
1. ✅ Replaced custom sanitization with nh3 library (modern replacement for deprecated bleach)
2. ✅ Added nh3==0.2.15 to requirements.txt
3. ✅ Implemented fallback to improved basic sanitization if nh3 unavailable
4. ✅ Added proper error logging for import failures
**Security Impact**: High - Prevents XSS attacks through user input
**Prevention**: Always use well-maintained security libraries, never roll custom security functions

---

## Error: Silent failures in authentication and models
**First Encountered**: Security audit on 2025-06-24
**Status**: ✅ RESOLVED on 2025-06-24
**Root Cause**: Multiple functions used bare `except: pass` blocks that suppressed all errors
**Locations Fixed**:
- `backend/auth.py:72-75` - load_user function silent failure
- `backend/models.py:275-278` - get_upcoming_bills RRULE parsing
- `backend/models.py:363-366` - get_upcoming_events RRULE parsing
**Solution Applied**:
1. ✅ Added specific exception handling with proper logging
2. ✅ Imported InvalidId for BSON ObjectId errors
3. ✅ Added warning logs for skipped items with detailed context
4. ✅ Replaced silent failures with informative error messages
**Prevention**: Always log exceptions, use specific exception types, never use bare except blocks

---

## Error: Dashboard responsive layout rendering failure
**First Encountered**: Security audit on 2025-06-24  
**Status**: ✅ RESOLVED on 2025-06-24
**Root Cause**: Dashboard hardcoded to render widgets only from 'lg' breakpoint layout
**Location Fixed**: `frontend/src/components/Dashboard.jsx:241-270`
**Impact**: Dashboard appeared empty on mobile and tablet devices
**Solution Applied**:
1. ✅ Changed from `currentLayouts?.lg?.map()` to `Object.keys(WIDGET_COMPONENTS).map()`
2. ✅ Updated widget key references from `item.i` to `widgetKey`
3. ✅ Fixed widget ID prop passing for responsive rendering
**Prevention**: Test responsive behavior on all breakpoints, avoid hardcoding breakpoint-specific logic

---

## Error: Performance issue with bulk todo operations
**First Encountered**: Security audit on 2025-06-24
**Status**: ✅ RESOLVED on 2025-06-24
**Root Cause**: Frontend sent N individual DELETE requests instead of single bulk request
**Locations Fixed**:
- `backend/routes.py:519-565` - Added bulk completion endpoint
- `backend/models.py:428-452` - Added bulk deletion methods
- `frontend/src/components/widgets/TodosWidget.jsx:105-121` - Updated to use bulk API
**Solution Applied**:
1. ✅ Implemented `/api/todos/complete-multiple` POST endpoint
2. ✅ Added `TodoModel.delete_multiple_todos()` method with ObjectId validation
3. ✅ Added rate limiting (max 100 todos per bulk operation)
4. ✅ Updated frontend to use `completeMultipleTodos()` API call
5. ✅ Added proper error handling and user feedback
**Performance Impact**: Reduced from N requests to 1 request for bulk operations
**Prevention**: Always implement bulk operations for multi-item actions

---

## Error: Production configuration with development flag
**First Encountered**: Security audit on 2025-06-24
**Status**: ✅ RESOLVED on 2025-06-24
**Root Cause**: systemd service configuration included `--reload` flag intended for development
**Location Fixed**: `personal-organizer.service:26`
**Impact**: Unnecessary overhead and potential service instability in production
**Solution Applied**: 
1. ✅ Removed `--reload` flag from gunicorn command
2. ✅ Maintained all other production settings (workers, timeouts, logging)
**Prevention**: Separate development and production configurations, review service files before deployment

---

## Error: can't subtract offset-naive and offset-aware datetimes
**First Encountered**: Multiple instances verified on 2025-06-24
**Status**: ✅ RESOLVED on 2025-06-24
**Root Cause**: Database contains timezone-naive datetime objects, application code uses timezone-aware datetime objects
**Locations Fixed**: 
- `backend/models.py:402` - Added missing `BaseModel.ensure_utc_aware()` method
- `backend/auth.py:143` - Fixed timezone subtraction in `refresh_weather_on_login()`
- `backend/models.py:193` - Fixed contact last_contact comparison
- `backend/models.py:276` - Fixed bill due_date comparison  
- `backend/models.py:359` - Fixed event start comparison
- `backend/models.py:351` - Fixed event duration calculation
**Solution Applied**: 
1. ✅ Implemented `BaseModel.ensure_utc_aware()` method
2. ✅ Updated all datetime comparisons to use timezone-aware conversion
3. ✅ Fixed authentication flow timezone handling
**Prevention**: Always use timezone-aware datetimes and implement proper conversion utilities

---

## Error: E11000 duplicate key error collection: personal_organizer.weather_cache index: city_1
**First Encountered**: 2025-06-24
**Status**: ✅ RESOLVED on 2025-06-24
**Root Cause**: MongoDB collection had incorrect unique index on 'city' field that doesn't exist in documents
**Location**: MongoDB weather_cache collection indexes
**Solution Applied**: Dropped the problematic 'city_1' unique index using `db.weather_cache.dropIndex('city_1')`
**Prevention**: Ensure database schema matches actual document structure

---

## Error: Frontend API routing mismatch (/api/dashboard/ vs /api/dashboard)
**First Encountered**: 2025-06-24
**Status**: 🔍 IDENTIFIED - Needs investigation
**Root Cause**: Frontend calling `/api/dashboard/` (with trailing slash) but backend expects `/api/dashboard` (without slash)
**Symptoms**:
- Frontend shows "Failed to load dashboard" 
- Backend logs show repeated 404 errors for `/api/dashboard/`
- Authentication check returns 200 but dashboard fetch fails
**Location**: Frontend API client or routing configuration
**Next Steps**: Investigate frontend API URL construction and routing setup
**Prevention**: Consistent API endpoint definitions between frontend and backend

---

## Error: Authentication system verification
**First Encountered**: 2025-06-24  
**Status**: ✅ VERIFIED WORKING on 2025-06-24
**Test Results**:
- ✅ Backend login API works correctly with credentials: admin/change-this-password
- ✅ Dashboard API returns proper data when authenticated via curl
- ✅ Session management functional
- ✅ Weather data, bills, contacts, events, todos all returned properly
**Remaining Issue**: Frontend-backend API integration has routing mismatch