# Personal Organizer - System Patterns & Architecture

## Architecture Overview

### Full-Stack Architecture
- **Frontend**: React SPA with Vite build system
- **Backend**: Flask REST API with MongoDB
- **Communication**: HTTP/JSON API calls via Axios client
- **Authentication**: Flask-Login session-based authentication

### Design Patterns

#### 1. Widget Pattern
- **Purpose**: Modular dashboard components for different data types
- **Implementation**: Each widget is a self-contained React component
- **Benefits**: Easy to add/remove features, independent data fetching
- **Components**: WeatherWidget, BillsWidget, TodosWidget, EventsWidget, ContactsWidget, ChartWidget

#### 2. API Client Pattern  
- **Purpose**: Centralized HTTP communication with backend
- **Implementation**: Axios-based client with dedicated modules per resource
- **Structure**: 
  - `client.js`: Base Axios configuration with interceptors
  - `auth.js`: Authentication endpoints
  - `bills.js`: Bills CRUD operations
  - `contacts.js`: Contact management
  - `events.js`: Calendar events
  - `todos.js`: Todo operations
  - `weather.js`: Weather data proxy

#### 3. Hook Pattern
- **Purpose**: Reusable state logic across components
- **Implementation**: Custom React hooks for common functionality
- **Examples**:
  - `useAuth`: Authentication state management
  - `useTheme`: Theme switching (light/dark mode)

#### 4. Repository Pattern (Backend)
- **Purpose**: Data access abstraction
- **Implementation**: MongoDB operations through model classes
- **Benefits**: Centralized data logic, easier testing, schema validation

### Data Flow Architecture

#### 1. Authentication Flow
```
Login Form → auth.js → Flask /api/auth/login → Flask-Login → Session Cookie → Dashboard
```

#### 2. Widget Data Flow
```
Widget Component → API Client → Flask Route → MongoDB → JSON Response → Widget State Update
```

#### 3. Dashboard Layout Flow
```
Grid Layout Component → Drag Event → Layout API → MongoDB User Document → Persistent Layout
```

### Component Hierarchy

```
App (Router + Auth Context)
├── LoginPage
└── Dashboard (Grid Layout)
    ├── Header (Navigation + Profile)
    ├── WeatherWidget (St. Louis + Accra)
    ├── BillsWidget (CRUD + Recurrence)
    ├── TodosWidget (Virtualized List)
    ├── EventsWidget (Calendar + Recurrence)
    ├── ContactsWidget (Threshold Tracking)
    └── ChartWidget (Bills Visualization)
```

### State Management Strategy

#### 1. Local Component State
- Used for widget-specific data and UI state
- React useState and useEffect hooks
- No global state management library (Redux/Zustand) needed

#### 2. Authentication State
- Managed via useAuth hook
- Session-based with Flask-Login
- Automatic redirect on authentication failure

#### 3. Persistent State
- Dashboard layout stored in MongoDB user document
- Widget preferences and categories stored per user
- No client-side persistence (localStorage/sessionStorage)

### Error Handling Patterns

#### 1. API Error Handling
- Axios interceptors for global error handling
- Component-level error boundaries for widget failures
- User-friendly error messages with fallback displays

#### 2. Form Validation
- Client-side validation for immediate feedback
- Server-side validation for security
- Consistent error message format across API endpoints

### Performance Patterns

#### 1. Data Caching
- Weather data cached for 2 hours (TTL index)
- Component-level caching for expensive operations
- MongoDB indexes for efficient queries

#### 2. Lazy Loading
- React.lazy() for code splitting
- Virtualized lists for large todo collections
- Image lazy loading for weather icons

#### 3. Bundle Optimization
- Vite code splitting by feature
- Vendor chunk separation (React, UI libraries, utilities)
- Tree shaking for unused code elimination