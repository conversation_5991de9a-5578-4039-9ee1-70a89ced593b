# Personal Organizer - Technology Context

## Technology Stack

### Frontend Technologies
- **React 18**: Component-based UI library with hooks
- **Vite**: Fast build tool and dev server (replaces Create React App)
- **Tailwind CSS**: Utility-first CSS framework for styling
- **React Grid Layout**: Drag-and-drop dashboard grid system
- **Recharts**: Declarative charting library for data visualization
- **Axios**: HTTP client for API communication
- **React Router**: Client-side routing for SPA navigation

### Backend Technologies
- **Python 3.9+**: Programming language
- **Flask**: Lightweight web framework for REST API
- **Flask-Login**: Session management for authentication
- **Flask-CORS**: Cross-origin resource sharing configuration
- **Flask-Limiter**: Rate limiting for API endpoints
- **PyMongo**: MongoDB Python driver
- **PBKDF2**: Password hashing algorithm
- **Gunicorn**: WSGI HTTP server for production deployment

### Database Technology
- **MongoDB**: NoSQL document database
- **Collections**: users, bills, events, contacts, todos, weather_cache, contact_types
- **Indexes**: Optimized for user queries and TTL cleanup
- **TTL Indexes**: Automatic cleanup for weather (2 hours) and todos (24 hours)

### Development Tools
- **npm**: Package manager for frontend dependencies
- **pip**: Package manager for Python dependencies
- **systemd**: Service management for production deployment
- **PostCSS**: CSS processing for Tailwind compilation

## Environment Setup

### Required System Dependencies
```bash
# Node.js 18+ and npm
node --version  # Should be 18.x or higher
npm --version

# Python 3.9+ and pip
python3 --version  # Should be 3.9 or higher
pip --version

# MongoDB 4.4+
mongod --version  # Should be 4.4 or higher
```

### Development Environment Variables
```bash
# Core Configuration
SECRET_KEY=your-secret-key-here
MONGO_URI=mongodb://localhost:27017/personal_organizer
FRONTEND_URL=http://localhost:5173

# Admin User Credentials
ADMIN_USERNAME=admin
ADMIN_PASSWORD=change-this-password

# Optional: Weather API Coordinates
STL_LAT=38.6270
STL_LON=-90.1994
ACCRA_LAT=5.6037
ACCRA_LON=-0.1870
```

### Production Environment Variables
```bash
# Production Configuration
FLASK_ENV=production
MONGO_URI=mongodb://localhost:27017/personal_organizer
FRONTEND_URL=https://your-domain.com
SECRET_KEY=secure-production-key

# Admin Credentials (Change these!)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=secure-production-password
```

## Architecture Constraints

### Single-User System
- **Constraint**: Application designed for exactly one user
- **Implication**: No user registration, simplified authentication
- **Benefit**: Reduced complexity, better performance

### MongoDB Document Design
- **Constraint**: NoSQL document structure
- **Implication**: Embedded documents for user preferences and layouts
- **Benefit**: Flexible schema, efficient queries for single-user data

### Time Zone Handling
- **Constraint**: All dates stored in UTC
- **Implication**: Display conversion to America/Chicago timezone
- **Benefit**: Consistent data storage, proper DST handling

### Weather API Limitation
- **Constraint**: Hardcoded for St. Louis and Accra only
- **Implication**: No user-configurable locations
- **Benefit**: Simplified caching and API management

### Contact Type Restrictions
- **Constraint**: Fixed thresholds (Family: 7 days, Friends: 14-21 days, Business: 30 days)
- **Implication**: No user-configurable contact types
- **Benefit**: Consistent color-coding and alert system

## Development Workflow

### Frontend Development
```bash
cd frontend/
npm install
npm run dev  # Starts Vite dev server on http://localhost:5173
```

### Backend Development
```bash
cd backend/
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
python app.py  # Starts Flask server on http://localhost:5000
```

### Database Development
```bash
# Start MongoDB
sudo systemctl start mongod

# Verify connection
mongo --eval "db.runCommand('ping')"
```

### Production Deployment
```bash
# Frontend build
cd frontend/
npm run build  # Creates dist/ directory

# Backend with Gunicorn
cd backend/
gunicorn --bind 0.0.0.0:5000 wsgi:app

# System service
sudo systemctl start personal-organizer
```

## API Design Constraints

### REST API Endpoints
- **Pattern**: `/api/<resource>` for all endpoints
- **Authentication**: Required for all endpoints except login
- **Rate Limiting**: 5 login attempts per minute, 200 requests per day
- **CORS**: Restricted to FRONTEND_URL origin only

### Request/Response Format
- **Content-Type**: application/json for all API calls
- **Error Format**: `{"error": "message", "code": 400}`
- **Success Format**: `{"data": {...}, "message": "success"}`
- **Date Format**: ISO 8601 UTC timestamps

### MongoDB Query Patterns
- **User Scoping**: All queries filtered by user_id
- **Indexing**: Compound indexes for user_id + resource fields
- **Aggregation**: Used for dashboard data compilation
- **TTL**: Automated cleanup for temporary data