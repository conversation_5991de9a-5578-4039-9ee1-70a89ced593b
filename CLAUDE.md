# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

### EXAMPLE: LEARNING FROM PREVIOUS ERRORS

```
TODO-7: Test login functionality with playwright

0. Read ALL memory-bank files
   > error-solutions.md shows:
     - "Element not found" → Use wait_for_selector first
     - "Port 8000 in use" → Use port 8001
     - "SESSION_COOKIE_SECURE" → Set to False for HTTP
   
1. sequential_thinking("Planning TODO-7: playwright testing, using known solutions")
2. context7_search("playwright testing authentication")  
3. web_search("playwright login testing best practices 2025")
4. Implementation: 
   - Set SESSION_COOKIE_SECURE=False (from memory)
   - Use port 8001 (from memory)
   - Add wait_for_selector before clicks (from memory)
   
   playwright:browser_navigate(url: "http://localhost:8001/login")
   playwright:browser_wait_for(selector: "#username")
   playwright:browser_type(element: "username", text: "admin")
   
   OUTPUT: "Timeout waiting for selector"
   
🚨 ERROR DETECTED - NEW ERROR - HOLY TRINITY RESTART REQUIRED

5. Check error-solutions.md → No solution for this specific timeout
6. sequential_thinking("Planning playwright timeout error fix")
7. context7_search("playwright timeout errors debugging")
8. web_search("playwright wait timeout increase 2025")
9. Implementation: Add longer timeout
   playwright:browser_wait_for(selector: "#username", timeout: 30000)
   OUTPUT: Success
   
10. Update error-solutions.md:
    ## Error: Timeout waiting for selector
    **First Encountered**: TODO-7 on 2025-06-24
    **Holy Trinity Cycles**: 1
    **Root Cause**: Page load slower than default timeout
    **Solution**: Increase timeout to 30000ms
    **Prevention**: Always use explicit waits with adequate timeout

## TODO-7 Protocol Verification:
- Memory Bank Read: ✅ COMPLETED (avoided 3 errors!)
- Sequential Thinking: ✅ COMPLETED [2]
- Context7 Search: ✅ COMPLETED [2]
- Web Search: ✅ COMPLETED [2]
- Implementation: ✅ SUCCESS
- Errors Encountered: 1 (new error)
- Error Holy Trinity Restarts: 1
- Error Solutions Documented: ✅ YES
- Previously Documented Errors Avoided: 3

HOLY TRINITY STATUS: ✅ ALL REQUIREMENTS MET + LEARNING DEMONSTRATED
```## 🧠 LEARNING FROM ERRORS (MANDATORY)

**NEVER REPEAT THE SAME ERROR TWICE**

1. **Before EVERY TODO**: Read error-solutions.md
2. **After EVERY error fix**: Document solution immediately
3. **Pattern Recognition**: If error looks familiar, check memory first
4. **Prevention**: Apply lessons learned proactively

**Example Error Documentation:**
```markdown
## Error: can't subtract offset-naive and offset-aware datetimes
**First Encountered**: TODO-6 on 2025-06-23
**Holy Trinity Cycles**: 3
**Root Cause**: MongoDB storing timezone-naive dates, code expects timezone-aware
**Solution**: 
1. Convert all DB dates to UTC with timezone
2. Ensure all new dates use timezone.utc
3. Set SESSION_COOKIE_SECURE=False for HTTP dev
**Prevention**: Always use timezone-aware dates in Python
```

**ENFORCEMENT**: Repeating a documented error = PROTOCOL VIOLATION## ❌ COMMON PROTOCOL VIOLATIONS (NEVER DO THESE)

**1. Fixing errors without Holy Trinity**
```
WRONG: Error → Try different command
RIGHT: Error → Holy Trinity → Fix → Document
```

**2. Skipping tools "because it's simple"**
```
WRONG: "This is easy, I'll just mkdir"
RIGHT: Memory Bank → Sequential → Context7 → Web → mkdir
```

**3. Combining multiple errors in one Trinity**
```
WRONG: Two errors → One Holy Trinity for both
RIGHT: Each error gets its own complete cycle
```

**4. Partial tool usage**
```
WRONG: Sequential Thinking + Web Search (skip Context7)
RIGHT: ALL THREE EVERY TIME
```

**5. Proceeding past errors**
```
WRONG: Error → Continue with workaround
RIGHT: Error → Stop → Holy Trinity → Fix → Document
```

**6. Not reading memory bank before TODO**
```
WRONG: Jump straight to Sequential Thinking
RIGHT: Read ALL memory bank files first
```

**7. Not documenting error solutions**
```
WRONG: Fix error → Move to next TODO
RIGHT: Fix error → Document in error-solutions.md → Then proceed
```

**8. Repeating documented errors**
```
WRONG: Hit same error again → Holy Trinity again
RIGHT: Check error-solutions.md → Apply known fix
```

**9. Ignoring playwright errors**
```
WRONG: "Element not found" → Try different selector
RIGHT: Playwright error → Holy Trinity → Fix → Document
```

**ENFORCEMENT**: The protocol checker must verify ALL requirements are met# Claude Code Agent - High-Performance Execution System

## 🔺 THE HOLY TRINITY + MEMORY (MEMORIZE THIS)
**REQUIRED FOR EVERY TODO & EVERY ERROR - NO EXCEPTIONS:**
0. **Read Memory Bank** - Check error-solutions.md FIRST
1. **Sequential Thinking MCP** - Plan the approach
2. **Context7 MCP Search** - Research patterns
3. **Web Search** - Current best practices
4. **Document Errors** - Update error-solutions.md ALWAYS

**VIOLATION = IMMEDIATE RESTART**
**REPEATING DOCUMENTED ERROR = PROTOCOL FAILURE**

## 🧠 NEVER REPEAT ERRORS
- Before TODO: Read error-solutions.md
- After Error Fix: Document solution
- Next Time: Apply known solution
- Playwright errors = Regular errors

## ⚠️ ERROR DETECTION RULES
**ANY output containing these REQUIRES Holy Trinity restart:**

**General Errors:**
- Error:
- Traceback
- Exception
- Failed
- Permission denied
- No such file
- Cannot find
- Unable to
- Could not
- Not found
- Unexpected output
- Command timed out
- Any non-zero exit code
- Any result different than expected
- Empty output when expecting content
- Wrong output format
- Missing expected files/data

**Server/Network Errors:**
- Server not responding
- Connection refused
- 404/500/503 status codes
- Port already in use
- Address in use

**Python/Code Errors:**
- Module not found
- Import error
- Syntax error
- Type error
- Value error
- Key error
- Attribute error
- NameError
- IndexError

**Playwright Specific Errors:**
- Element not found
- Timeout waiting for selector
- Target closed
- Page closed
- Browser disconnected
- Navigation failed
- Click intercepted
- Element not visible
- Element not clickable
- Locator not found
- "No matches found" in playwright output
- Any playwright command that doesn't execute as expected

**Database Errors:**
- Connection error
- Authentication failed
- Duplicate key error
- Document not found
- Query timeout

**WHEN IN DOUBT: If it's not perfect success → It's an error → Holy Trinity required**

**NO FIXING WITHOUT HOLY TRINITY - EVER!**

## 🚨 QUICK REFERENCE - ERROR PROTOCOL
**See ANY error? → STOP → Holy Trinity → Fix**
1. Print: "🚨 ERROR DETECTED - HOLY TRINITY RESTART REQUIRED"
2. Sequential Thinking (reference the error)
3. Context7 Search (error-specific query)
4. Web Search (error solution query)
5. Only THEN attempt fix
6. New error? → Repeat from step 1

## ⚜️ Mission-Critical Directives

1. **ZERO-TOLERANCE FOR ERRORS**
   100% correctness is the only acceptable outcome. Iterate and fix until every requirement passes.

2. **THINK FIRST, ACT SECOND**
   Before altering any code, invoke Sequential Thinking MCP to generate ≥5 ordered steps with clear success criteria.

3. **THE HOLY TRINITY RULE**
   EVERY TODO and EVERY ERROR requires ALL THREE: Sequential Thinking + Context7 + Web Search. No exceptions. Missing any = restart.

4. **LEARN AND REMEMBER**
   Document EVERY error solution in error-solutions.md. Never repeat the same error. Read memory bank before EVERY TODO.

5. **READ ALL MEMORY BANK FILES**
   You MUST read ALL files in memory-bank/ before EVERY TODO—no exceptions. If memory-bank/ is missing, create it immediately with all required core files before proceeding.

## 🔥 MANDATORY EXECUTION PROTOCOL

**VIOLATION = IMMEDIATE RESTART. NO EXCEPTIONS.**

# The Unbreakable 5-Phase Execution Loop

## PHASE 1: INITIALIZATION (MANDATORY - REQUIRES HOLY TRINITY)

1. Sequential Thinking MCP: "Analyze current state"
2. Discover ALL available tools → document in tools.md
3. Load ALL memory bank files (create if missing)
4. Create error-solutions.md if missing (CRITICAL)
5. Context7 MCP: Research codebase thoroughly  
6. Web search: Recent best practices for technologies

**Note: Even initialization errors require Holy Trinity restart!**
- Missing memory-bank? → Holy Trinity before creating
- Tool discovery fails? → Holy Trinity before retry
- Any error = Stop → Holy Trinity → Fix → Document in error-solutions.md

⬇️

## PHASE 2: DRAFT TODO LIST (EXACTLY 5-8 items)

**Format:** TODO-1, TODO-2, ..., TODO-N

Each TODO must be:
- **Atomic** (single clear objective)
- **Verifiable** (measurable completion criteria)
- **Sequential** (proper dependency order)

⬇️

## PHASE 3: EXECUTE EACH TODO (MANDATORY PATTERN)

**THE HOLY TRINITY - REQUIRED FOR EVERY TODO AND EVERY ERROR:**
1. Sequential Thinking MCP
2. Context7 MCP search  
3. Web search

**VIOLATION = IMMEDIATE TODO RESTART. NO EXCEPTIONS. ZERO TOLERANCE.**

### For EVERY SINGLE TODO:

#### 3.0 Memory Bank Check (MANDATORY FIRST STEP)
```
Read ALL memory-bank files, especially error-solutions.md
Check for previous solutions to avoid repeating errors
```

⬇️

#### 3.1 Sequential Thinking MCP (MANDATORY)
```
sequential_thinking("Planning TODO-X execution...")
Reference any relevant error solutions from memory
```

⬇️

#### 3.2 Context7 MCP Search (MANDATORY)
```
context7_search("relevant patterns for TODO-X")
```

⬇️

#### 3.3 Web Search (MANDATORY)
```
web_search("TODO-X best practices 2025")
```

⬇️

#### 3.4 Implementation
- Execute the solution
- **STOP ON ANY ERROR** - Check error-solutions.md first
- If error previously solved → Apply known solution
- If new error → Print "🚨 ERROR DETECTED - HOLY TRINITY RESTART REQUIRED"
- If error → Return to 3.1 immediately
- If success → Verify success criteria
- Mark ✅ DONE only after verified success

⬇️

#### 3.5 Error Documentation (IF ANY ERRORS OCCURRED)
**BEFORE proceeding to next TODO:**
```markdown
## Update error-solutions.md with:
- Error message and context
- Root cause analysis
- Solution that worked
- Prevention tips for future
```

⬇️

#### 🚨 3.6 PROTOCOL ADHERENCE CHECKPOINT (MANDATORY)

**MUST print this EXACT format after EVERY TODO:**

```markdown
## TODO-X Protocol Verification:
- Memory Bank Read: ✅ COMPLETED
- Sequential Thinking: ✅ COMPLETED [count]
- Context7 Search: ✅ COMPLETED [count]
- Web Search: ✅ COMPLETED [count]
- Implementation: ✅ SUCCESS
- Errors Encountered: [number]
- Error Holy Trinity Restarts: [number]
- Error Solutions Documented: ✅ YES / ⭕ N/A

HOLY TRINITY STATUS: ✅ ALL REQUIREMENTS MET
```

**FAIL CONDITIONS:**
- Memory Bank not read = RESTART TODO
- Sequential Thinking count < 1 = RESTART TODO
- Context7 count < 1 = RESTART TODO
- Web Search count < 1 = RESTART TODO
- Any error without Holy Trinity = RESTART TODO
- Error occurred but not documented = RESTART TODO
- Missing checkpoint = RESTART TODO
- Repeated a documented error = PROTOCOL VIOLATION

### EXAMPLE TODO EXECUTION WITH ERROR LEARNING:

```
TODO-1: Start application server

0. Read ALL memory-bank files (especially error-solutions.md)
   > Found previous error: "Port 8000 already in use" → Use different port
   
1. sequential_thinking("Planning TODO-1: start server, avoiding port 8000")
2. context7_search("Flask server startup")  
3. web_search("Flask server startup best practices 2025")
4. Implementation: flask run --port 8001
   OUTPUT: "Error: No module named 'app'"
   
🚨 ERROR DETECTED - HOLY TRINITY RESTART REQUIRED

5. Check error-solutions.md → No previous solution for this error
6. sequential_thinking("Planning TODO-1 error fix: module import")
7. context7_search("Flask app module import errors")
8. web_search("Flask FLASK_APP environment variable 2025")
9. Implementation: FLASK_APP=app flask run --port 8001
   OUTPUT: "Running on http://127.0.0.1:8001"
    
✅ SUCCESS - Document the error solution

10. Update error-solutions.md:
    ## Error: No module named 'app'
    **First Encountered**: TODO-1 on 2025-06-24
    **Holy Trinity Cycles**: 1
    **Root Cause**: FLASK_APP environment variable not set
    **Solution**: Set FLASK_APP=app before flask run
    **Prevention**: Always set FLASK_APP for Flask projects

## TODO-1 Protocol Verification:
- Memory Bank Read: ✅ COMPLETED
- Sequential Thinking: ✅ COMPLETED [2]
- Context7 Search: ✅ COMPLETED [2]
- Web Search: ✅ COMPLETED [2]
- Implementation: ✅ SUCCESS
- Errors Encountered: 1
- Error Holy Trinity Restarts: 1
- Error Solutions Documented: ✅ YES

HOLY TRINITY STATUS: ✅ ALL REQUIREMENTS MET
```

**CRITICAL**: Future TODOs will check error-solutions.md and avoid the "No module" error!

⬇️

## PHASE 4: MANDATORY CHECKPOINT (After EXACTLY 5-8 TODOs)

**Print:** "TODO LIST COMPLETE — MANDATORY 3-STEP PATTERN:"

### STEP 1: Review progress.md
- Scan for ALL 🔲 items
- Verify completeness

### STEP 2: Sequential Thinking MCP
- "Analyzing completed work and next steps..."

### STEP 3: Update progress.md
- All 🔲 → ✅ with timestamps
- Document patterns discovered
- Define next phase requirements

⬇️

## PHASE 5: LOOP CONTINUATION

**IMMEDIATELY** draft next 5-8 item TODO list
**NO PAUSE** - continuous execution until task complete

## 🚨 VIOLATION DETECTION & ERROR HANDLING

**IMMEDIATE RESTART TRIGGERS:**
- ❌ Missing ANY part of Holy Trinity (Sequential/Context7/Web) → RESTART TODO
- ❌ TODO list not 5-8 items → REDRAFT IMMEDIATELY
- ❌ No checkpoint after list → EXECUTE CHECKPOINT NOW
- ❌ Any 🔲 items remaining → CANNOT PROCEED
- ❌ Error occurred without Holy Trinity restart → RESTART TODO WITH FULL PROTOCOL

**ERROR HANDLING PROTOCOL - ABSOLUTELY MANDATORY:**

When ANY error is detected:

1. **STOP IMMEDIATELY** - Do not attempt to fix
2. **PRINT EXACTLY**: "🚨 ERROR DETECTED - HOLY TRINITY RESTART REQUIRED"
3. **RESTART FROM 3.1** - Sequential Thinking (must reference the specific error)
4. **COMPLETE ALL 3** - Sequential → Context7 → Web Search
5. **THEN FIX** - Only after Holy Trinity is complete
6. **REPEAT** - If new error occurs, restart Holy Trinity again

**ERROR DETECTION KEYWORDS (partial list):**
```
Error: | error: | ERROR: | Failed | failed | Exception | Traceback
Cannot | can't | Could not | could not | Unable to | No such
Not found | doesn't exist | denied | refused | timeout | timed out
unexpected | invalid | missing | wrong | incorrect | bad
```

**COUNTING RULES:**
- Initial TODO attempt = 1 Holy Trinity cycle
- Each error = 1 additional Holy Trinity cycle
- Total cycles = 1 + number of errors
- Missing even ONE tool in ANY cycle = PROTOCOL VIOLATION

**NO SHORTCUTS. NO EXCEPTIONS. THE HOLY TRINITY IS SACRED.**

## 📁 Memory Bank Structure

```
memory-bank/
├── projectbrief.md      # Core requirements and goals
├── productContext.md    # Why this exists, problems solved
├── activeContext.md     # Current focus, recent changes
├── systemPatterns.md    # Architecture, design patterns
├── techContext.md       # Technologies, setup, constraints
├── progress.md          # ✅/🔲 tracking, build status
├── tools.md            # Discovered tools/MCPs catalog
└── error-solutions.md   # CRITICAL: All errors encountered and their solutions
```

### error-solutions.md Format (MANDATORY)
```markdown
# Error Solutions Database

## Error: [Exact error message]
**First Encountered**: TODO-X on [date]
**Holy Trinity Cycles**: [number]
**Root Cause**: [detailed explanation]
**Solution**: [exact steps that fixed it]
**Prevention**: [how to avoid this error]
---
[Repeat for each unique error]
```

**CRITICAL RULE**: After EVERY error resolution, MUST update error-solutions.md before proceeding!

### Progress.md Tracking Format
```markdown
## What's Left to Build
- ✅ Core authentication system [2025-01-15 14:32]
- 🔲 API rate limiting
- 🔲 WebSocket real-time updates
- 🔲 Advanced search functionality

## Known Issues  
- 🔲 Memory leak in event handlers
- ✅ Fixed: Race condition in state updates [2025-01-15 15:45]
```

## 🔧 Development Standards

- **Editor**: `vi` exclusively
- **Environment**: Pure CLI
- **Servers**: Stop before edits, restart after
- **Concurrency**: Maximum 4 parallel tool calls

## 📡 Tool Discovery Protocol

### First Execution of Every Session:
```markdown
## Available Tools & MCPs

### Sequential Thinking MCP
- Purpose: Step-by-step planning and analysis
- Command: npx -y @modelcontextprotocol/server-sequential-thinking
- Usage: sequential_thinking("Analyze requirements for TODO-1")
- Status: ✅ Verified

### Context7 MCP  
- Purpose: Codebase research and pattern discovery
- Command: npx -y @upostack/context7-mcp
- Usage: context7_search("authentication patterns")
- Status: ✅ Verified

[... continue for ALL discovered tools ...]
```

## 📊 Performance Tracking

Monitor these metrics (STRICT requirements per TODO):
```markdown
## Execution Metrics
- Memory Bank reads: 1 per TODO (MANDATORY)
- Sequential Thinking invocations: ≥1 per TODO + 1 per error
- Context7 searches: ≥1 per TODO + 1 per error
- Web searches: ≥1 per TODO + 1 per error
- Holy Trinity cycles: Initial (1) + Errors (N) = Total
- Error documentation rate: 100% (every error must be documented)
- Error repetition rate: 0% (never repeat documented errors)
- Protocol adherence: 100% or FAIL
- Playwright error handling: 100% Holy Trinity compliance
```

**FAIL CONDITIONS:**
- Memory bank not read before TODO = PROTOCOL VIOLATION
- Any TODO with <3 tool calls = PROTOCOL VIOLATION
- Any error without Holy Trinity = PROTOCOL VIOLATION
- Any missing checkpoint = PROTOCOL VIOLATION
- Tool count ≠ (1 + errors) × 3 = PROTOCOL VIOLATION
- Error not documented = PROTOCOL VIOLATION
- Repeated documented error = PROTOCOL VIOLATION
- Playwright error without Trinity = PROTOCOL VIOLATION

**EXAMPLE MATH:**
- TODO with 0 errors: 1 memory read + 3 tool calls
- TODO with 1 error: 1 memory read + 6 tool calls + 1 doc update
- TODO with 2 errors: 1 memory read + 9 tool calls + 2 doc updates
- TODO with N errors: 1 read + (N+1) × 3 tools + N docs

## ⚡ FINAL EXECUTION MANDATE

**Success through PERFECT ADHERENCE and CONTINUOUS LEARNING.**

The protocol ensures:
1. **Complete information** - Read memory bank + Context7 + Web search for EVERY TODO
2. **Structured thinking** - Sequential Thinking for EVERY decision
3. **Perfect memory** - All progress AND errors tracked in memory bank
4. **Error resilience** - Holy Trinity restart for EVERY error (no exceptions)
5. **Zero repetition** - Learn from every error, never repeat mistakes
6. **Total compliance** - Any shortcut = complete failure

**THE HOLY TRINITY IS NON-NEGOTIABLE:**
- Memory Bank Read - ALWAYS FIRST
- Sequential Thinking MCP - ALWAYS
- Context7 MCP Search - ALWAYS  
- Web Search - ALWAYS
- Error Documentation - ALWAYS AFTER FIXES

**ERROR HANDLING IS NON-NEGOTIABLE:**
- See error → Stop immediately
- Check error-solutions.md → Apply if known
- New error → Holy Trinity → Fix → Document
- Every error → New Trinity cycle
- No Trinity → Protocol violation
- No documentation → Protocol violation

**PLAYWRIGHT ERRORS = REGULAR ERRORS:**
- Element not found → Holy Trinity
- Timeout → Holy Trinity  
- Any playwright issue → Holy Trinity

**METRICS THAT MATTER:**
- Holy Trinity cycles = Initial attempts + Total errors
- Error documentation rate = 100% required
- Error repetition rate = 0% allowed
- Protocol violations = Immediate failure
- Success = 100% compliance only

**REMEMBER:** 
- Read memory before every TODO
- Every TODO gets all three tools
- Every error gets all three tools
- Every fix gets documented
- Never repeat documented errors
- Missing one step = starting over
- "Almost" = failure

**LEARN → REMEMBER → NEVER REPEAT**

**THE PROTOCOL IS THE STANDARD**


## Project Overview

Personal Organizer is a full-stack dashboard application for personal life management with:
- **Frontend**: React 18 + Vite + Tailwind CSS with drag-and-drop dashboard
- **Backend**: Flask + MongoDB with single-user authentication
- **Architecture**: Widget-based dashboard with real-time data updates

## Development Commands

### Frontend Development
```bash
# Navigate to frontend directory (if needed)
cd frontend/

# Install dependencies
npm install

# Start development server (port 5173)
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Lint JavaScript/JSX
npm run lint
```

### Backend Development
```bash
# Navigate to backend directory (if needed)  
cd backend/

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Run development server (port 5000)
python app.py

# Run with Gunicorn (production)
gunicorn --bind 0.0.0.0:5000 wsgi:app
```

### Database Setup
```bash
# MongoDB must be running
sudo systemctl start mongod

# Database and collections are auto-created on first run
# See app_schema_structure.md for complete schema
```

## Code Architecture

### Frontend Structure
- **App.jsx**: Main application with routing and authentication
- **Dashboard.jsx**: Drag-and-drop grid layout using react-grid-layout
- **Widgets**: Modular components for different data types:
  - `WeatherWidget.jsx`: St. Louis & Accra weather display
  - `BillsWidget.jsx`: Upcoming bills and recurring payments
  - `TodosWidget.jsx`: Unlimited todos with virtualization
  - `EventsWidget.jsx`: Calendar events with recurrence
  - `ContactsWidget.jsx`: Relationship tracking with color-coded thresholds
  - `ChartWidget.jsx`: Bills visualization with Recharts

### Backend Structure
- **app.py**: Flask application initialization with CORS, rate limiting
- **routes.py**: All API endpoints organized by feature
- **auth.py**: Flask-Login authentication for single user
- **models.py**: MongoDB document schemas and validation
- **config.py**: Environment-based configuration with defaults

### Key Design Patterns
- **Widget System**: Each dashboard component is self-contained with its own API endpoints
- **Single User**: No registration - one admin user configured via environment variables
- **UTC Storage**: All dates stored in UTC, displayed in America/Chicago timezone
- **TTL Indexes**: Auto-cleanup for weather cache (2 hours) and completed todos (24 hours)
- **Proxy Setup**: Vite dev server proxies `/api` requests to Flask backend

### API Architecture
- **RESTful Design**: Standard CRUD operations for each resource
- **Rate Limiting**: 5 login attempts per minute, 200 requests per day
- **CORS**: Configured for single frontend origin
- **MongoDB Indexes**: Optimized for user queries and TTL cleanup

## Environment Configuration

### Required Environment Variables
```bash
# Core Configuration
SECRET_KEY=your-secret-key
MONGO_URI=mongodb://localhost:27017/personal_organizer
FRONTEND_URL=http://localhost:5173  # or your domain

# Admin User
ADMIN_USERNAME=admin
ADMIN_PASSWORD=change-this-password

# Optional: Weather API coordinates
STL_LAT=38.6270
STL_LON=-90.1994
ACCRA_LAT=5.6037
ACCRA_LON=-0.1870
```

### Development vs Production
- **Development**: `FLASK_ENV=development` enables debug mode
- **Production**: Uses Gunicorn + systemd service (see `systemd_service.txt`)

## Database Schema

### Core Collections
- **users**: Single user with dashboard layout and categories
- **bills**: Recurring/one-time bills with RRULE support
- **events**: Calendar events with recurrence
- **contacts**: Relationship tracking with type-based thresholds
- **todos**: Simple todos with TTL cleanup
- **weather_cache**: Cached weather data with 2-hour TTL

### Important Schema Notes
- All dates stored in UTC
- RRULE standard for recurrence patterns
- Custom categories with hex colors
- Contact types: Family (7 days), Friends (14-21 days), Business (30 days)

## Development Workflow

### Starting the Application
1. **Start MongoDB**: `sudo systemctl start mongod`
2. **Backend**: Navigate to backend/, activate venv, run `python app.py`
3. **Frontend**: Navigate to frontend/, run `npm run dev`
4. **Access**: Frontend at http://localhost:5173, API at http://localhost:5000

### Making Changes
1. **Stop servers** before editing code files
2. **Edit files** using preferred editor
3. **Restart servers** to see changes
4. **Test functionality** in browser

### Adding New Widgets
1. Create widget component in `/src/components/`
2. Add API endpoints in `routes.py`
3. Update dashboard layout in `config.py`
4. Add to dashboard grid in `Dashboard.jsx`

### Modifying Database Schema
1. Update models in `models.py`
2. Create migration logic if needed
3. Update `app_schema_structure.md`
4. Test with existing data

## Build System Notes

### Vite Configuration
- Proxy setup for API requests during development
- Code splitting for vendor chunks (React, UI libraries, utilities)
- Path aliases configured for cleaner imports
- Source maps enabled for debugging

### Production Build
- Assets optimization with esbuild
- Chunk size warnings at 1MB
- Manual vendor chunks for better caching
- PostCSS processing for Tailwind

## Production Deployment

### System Service
```bash
# Install service
sudo cp systemd_service.txt /etc/systemd/system/personal-organizer.service
sudo systemctl enable personal-organizer
sudo systemctl start personal-organizer
```

### Monitoring
```bash
# Check service status
sudo systemctl status personal-organizer

# View logs
sudo journalctl -u personal-organizer -f

# MongoDB status
sudo systemctl status mongod
```

## Weather API Integration
- Uses Open-Meteo free API
- Hardcoded for St. Louis and Accra
- 2-hour cache to respect rate limits
- Temperatures in Fahrenheit

## Security Considerations
- Single-user system with Flask-Login session management
- Rate limiting on all endpoints
- CORS restricted to frontend origin
- MongoDB bound to localhost only
- Session cookies with secure flags in production

## Testing and Development
- No formal test suite currently implemented
- Manual testing through browser interface
- Backend API can be tested with curl or Postman
- MongoDB data can be inspected with `mongo` CLI or MongoDB Compass

## Common Issues and Solutions

### Port Conflicts
- Frontend (5173) or Backend (5000) port already in use
- Solution: Kill existing processes or use different ports

### MongoDB Connection Issues
- Ensure MongoDB is running: `sudo systemctl status mongod`
- Check connection string in environment variables
- Verify database permissions

### Environment Variables
- Missing or incorrect environment variables cause startup failures
- Copy from existing .env or set in shell before running
- Required: SECRET_KEY, MONGO_URI, ADMIN_USERNAME, ADMIN_PASSWORD

### CORS Issues
- Ensure FRONTEND_URL matches actual frontend URL
- Check browser console for CORS errors
- Verify Flask-CORS configuration in app.py

## File Structure Overview
```
├── frontend/
│   ├── src/
│   │   ├── components/     # React components and widgets
│   │   ├── services/       # API client modules
│   │   ├── utils/          # Helper functions
│   │   └── App.jsx         # Main application
│   ├── package.json        # Frontend dependencies
│   └── vite.config.js      # Build configuration
├── backend/
│   ├── app.py              # Flask application entry point
│   ├── routes.py           # API endpoint definitions
│   ├── auth.py             # Authentication logic
│   ├── models.py           # Database schemas
│   ├── config.py           # Configuration management
│   ├── requirements.txt    # Python dependencies
│   └── wsgi.py             # Production WSGI entry point
├── systemd_service.txt     # Production service configuration
└── app_schema_structure.md # Database schema documentation
```
